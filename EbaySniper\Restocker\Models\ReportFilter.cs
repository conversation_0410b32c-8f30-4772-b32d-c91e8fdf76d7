using System;
using System.Collections.Generic;
using System.Linq;

namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents filtering options for generating reports
    /// </summary>
    public class ReportFilter
    {
        /// <summary>
        /// Start date for filtering transactions (inclusive)
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// End date for filtering transactions (inclusive)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Filter by specific Job ID
        /// </summary>
        public string JobId { get; set; }

        /// <summary>
        /// Filter by specific Keyword ID
        /// </summary>
        public string KeywordId { get; set; }

        /// <summary>
        /// Include completed transactions in the report
        /// </summary>
        public bool IncludeCompleted { get; set; } = true;

        /// <summary>
        /// Include failed transactions in the report
        /// </summary>
        public bool IncludeFailed { get; set; } = true;

        /// <summary>
        /// Include pending transactions in the report
        /// </summary>
        public bool IncludePending { get; set; } = true;

        /// <summary>
        /// Maximum number of records to return (0 = no limit)
        /// </summary>
        public int MaxRecords { get; set; } = 0;

        /// <summary>
        /// Validates that the filter settings are valid
        /// </summary>
        /// <returns>True if the filter is valid, false otherwise</returns>
        public bool IsValid()
        {
            // Check date range validity
            if (StartDate.HasValue && EndDate.HasValue && StartDate.Value > EndDate.Value)
            {
                return false;
            }

            // At least one status type must be included
            if (!IncludeCompleted && !IncludeFailed && !IncludePending)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// Gets the status filter values as an array
        /// </summary>
        /// <returns>Array of status values to include</returns>
        public string[] GetStatusFilters()
        {
            var statuses = new List<string>();

            if (IncludeCompleted) statuses.Add("Completed");
            if (IncludeFailed) statuses.Add("Failed");
            if (IncludePending) statuses.Add("Pending");

            return statuses.ToArray();
        }

        /// <summary>
        /// Checks if a transaction matches this filter
        /// </summary>
        /// <param name="transaction">The transaction to check</param>
        /// <returns>True if the transaction matches the filter</returns>
        public bool Matches(PurchaseTransaction transaction)
        {
            if (transaction == null) return false;

            // Check date range
            if (StartDate.HasValue && transaction.PurchaseDate < StartDate.Value)
                return false;

            if (EndDate.HasValue && transaction.PurchaseDate > EndDate.Value)
                return false;

            // Check Job ID
            if (!string.IsNullOrEmpty(JobId) && transaction.JobId != JobId)
                return false;

            // Check Keyword ID
            if (!string.IsNullOrEmpty(KeywordId) && transaction.KeywordId != KeywordId)
                return false;

            // Check status
            var statusFilters = GetStatusFilters();
            if (!statusFilters.Contains(transaction.Status))
                return false;

            return true;
        }
    }
}
