using System;

namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents a completed or attempted purchase transaction
    /// </summary>
    public class PurchaseTransaction
    {
        public int Id { get; set; }

        /// <summary>
        /// References the keyword's unique identifier from Keyword2Find.Id
        /// </summary>
        public string KeywordId { get; set; } = string.Empty;

        /// <summary>
        /// Job ID for grouping related purchases
        /// </summary>
        public string JobId { get; set; } = string.Empty;

        /// <summary>
        /// eBay item ID that was purchased
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Title of the purchased item
        /// </summary>
        public string ItemTitle { get; set; } = string.Empty;

        /// <summary>
        /// Price paid for the item
        /// </summary>
        public decimal PurchasePrice { get; set; }

        /// <summary>
        /// Number of items purchased in this transaction
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// When the purchase was made
        /// </summary>
        public DateTime PurchaseDate { get; set; }

        /// <summary>
        /// Status of the transaction: "Completed", "Failed", "Pending"
        /// </summary>
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// eBay transaction ID (if available)
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Payment method used
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Shipping address used for the purchase
        /// </summary>
        public string ShippingAddress { get; set; }

        /// <summary>
        /// Additional notes about the transaction
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// HTML content from the last purchase step (success or error page)
        /// </summary>
        public string LastStepHtml { get; set; }

        public PurchaseTransaction()
        {
            PurchaseDate = DateTime.UtcNow;
        }
    }
}
