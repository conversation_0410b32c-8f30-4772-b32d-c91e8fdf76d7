﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class ReportExportServiceTests
    {
        private ReportExportService _exportService;
        private string _testOutputDirectory;

        [TestInitialize]
        public void Setup()
        {
            _exportService = new ReportExportService();
            _testOutputDirectory = Path.Combine(Path.GetTempPath(), "RestockerReportTests");
            Directory.CreateDirectory(_testOutputDirectory);
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (Directory.Exists(_testOutputDirectory))
            {
                Directory.Delete(_testOutputDirectory, true);
            }
        }

        [TestMethod]
        public async Task ExportUserReportToCsvAsync_WithValidData_CreatesCorrectFile()
        {
            // Arrange
            var reports = new List<UserReport>
            {
                new UserReport
                {
                    KeywordId = "keyword-1",
                    JobId = "job-123",
                    KeywordAlias = "Test Product",
                    Keywords = "test,product",
                    RequiredQuantity = 5,
                    PurchasedQuantity = 3,
                    LastOrderStatus = "Completed",
                    LastTransactionTime = DateTime.Now,
                    PriceMin = 10.0,
                    PriceMax = 50.0
                },
                new UserReport
                {
                    KeywordId = "keyword-2",
                    JobId = "job-456",
                    KeywordAlias = "Another Product",
                    Keywords = "another,product",
                    RequiredQuantity = 2,
                    PurchasedQuantity = 1,
                    LastOrderStatus = "Failed",
                    LastTransactionTime = DateTime.Now.AddHours(-1),
                    PriceMin = 20.0,
                    PriceMax = 100.0
                }
            };

            var filePath = Path.Combine(_testOutputDirectory, "user_report.csv");

            // Act
            await _exportService.ExportUserReportToCsvAsync(reports, filePath);

            // Assert
            Assert.IsTrue(File.Exists(filePath));

            var lines = File.ReadAllLines(filePath);
            Assert.IsTrue(lines.Length >= 3); // Header + 2 data rows

            // Check header
            var header = lines[0];
            Assert.IsTrue(header.Contains("KeywordId"));
            Assert.IsTrue(header.Contains("JobId"));
            Assert.IsTrue(header.Contains("KeywordAlias"));
            Assert.IsTrue(header.Contains("RequiredQuantity"));
            Assert.IsTrue(header.Contains("PurchasedQuantity"));
            Assert.IsTrue(header.Contains("LastOrderStatus"));

            // Check first data row
            var firstRow = lines[1];
            Assert.IsTrue(firstRow.Contains("keyword-1"));
            Assert.IsTrue(firstRow.Contains("job-123"));
            Assert.IsTrue(firstRow.Contains("Test Product"));
            Assert.IsTrue(firstRow.Contains("5"));
            Assert.IsTrue(firstRow.Contains("3"));
            Assert.IsTrue(firstRow.Contains("Completed"));
        }

        [TestMethod]
        public async Task ExportTransactionDetailReportToCsvAsync_WithValidData_CreatesCorrectFile()
        {
            // Arrange
            var report = new TransactionDetailReport
            {
                KeywordId = "keyword-1",
                JobId = "job-123",
                KeywordAlias = "Test Product",
                Transactions = new List<PurchaseTransaction>
                {
                    new PurchaseTransaction
                    {
                        Id = 1,
                        ItemId = "item-456",
                        ItemTitle = "Test Item",
                        Status = "Completed",
                        Quantity = 2,
                        PurchasePrice = 25.99m,
                        PurchaseDate = DateTime.Now
                    }
                },
                Attempts = new List<PurchaseAttempt>
                {
                    new PurchaseAttempt
                    {
                        Id = 1,
                        ItemId = "item-456",
                        Result = "Success",
                        AttemptDate = DateTime.Now.AddMinutes(-5)
                    }
                }
            };

            var filePath = Path.Combine(_testOutputDirectory, "transaction_detail.csv");

            // Act
            await _exportService.ExportTransactionDetailReportToCsvAsync(report, filePath);

            // Assert
            Assert.IsTrue(File.Exists(filePath));

            var content = File.ReadAllText(filePath);
            Assert.IsTrue(content.Contains("keyword-1"));
            Assert.IsTrue(content.Contains("job-123"));
            Assert.IsTrue(content.Contains("Test Product"));
            Assert.IsTrue(content.Contains("item-456"));
            Assert.IsTrue(content.Contains("Completed"));
            Assert.IsTrue(content.Contains("Success"));
        }

        [TestMethod]
        public async Task SaveHtmlContentAsync_WithValidContent_CreatesHtmlFile()
        {
            // Arrange
            var htmlContent = "<html><body><h1>Purchase Success</h1><p>Your order has been placed.</p></body></html>";
            var keywordId = "keyword-1";
            var jobId = "job-123";
            var timestamp = DateTime.Now;

            // Act
            var filePath = await _exportService.SaveHtmlContentAsync(htmlContent, keywordId, jobId, timestamp);

            // Assert
            Assert.IsNotNull(filePath);
            Assert.IsTrue(File.Exists(filePath));

            var savedContent = File.ReadAllText(filePath);
            Assert.AreEqual(htmlContent, savedContent);

            // Check file naming convention
            var fileName = Path.GetFileName(filePath);
            Assert.IsTrue(fileName.Contains(keywordId));
            Assert.IsTrue(fileName.Contains(jobId));
            Assert.IsTrue(fileName.EndsWith(".html"));
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public async Task ExportUserReportToCsvAsync_WithNullReports_ThrowsException()
        {
            // Act
            await _exportService.ExportUserReportToCsvAsync(null, "test.csv");
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public async Task ExportUserReportToCsvAsync_WithEmptyFilePath_ThrowsException()
        {
            // Arrange
            var reports = new List<UserReport>();

            // Act
            await _exportService.ExportUserReportToCsvAsync(reports, "");
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public async Task ExportTransactionDetailReportToCsvAsync_WithNullReport_ThrowsException()
        {
            // Act
            await _exportService.ExportTransactionDetailReportToCsvAsync(null, "test.csv");
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public async Task SaveHtmlContentAsync_WithEmptyContent_ThrowsException()
        {
            // Act
            await _exportService.SaveHtmlContentAsync("", "keyword", "job", DateTime.Now);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public async Task SaveHtmlContentAsync_WithEmptyKeywordId_ThrowsException()
        {
            // Act
            await _exportService.SaveHtmlContentAsync("<html></html>", "", "job", DateTime.Now);
        }

        [TestMethod]
        public async Task ExportUserReportToCsvAsync_WithEmptyData_CreatesHeaderOnlyFile()
        {
            // Arrange
            var reports = new List<UserReport>();
            var filePath = Path.Combine(_testOutputDirectory, "empty_report.csv");

            // Act
            await _exportService.ExportUserReportToCsvAsync(reports, filePath);

            // Assert
            Assert.IsTrue(File.Exists(filePath));

            var lines = File.ReadAllLines(filePath);
            Assert.AreEqual(1, lines.Length); // Only header row

            var header = lines[0];
            Assert.IsTrue(header.Contains("KeywordId"));
            Assert.IsTrue(header.Contains("JobId"));
        }
    }
}
