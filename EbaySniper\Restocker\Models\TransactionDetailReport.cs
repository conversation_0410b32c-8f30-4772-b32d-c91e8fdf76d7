using System;
using System.Collections.Generic;
using System.Linq;

namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents a detailed report for a specific transaction and its steps
    /// </summary>
    public class TransactionDetailReport
    {
        /// <summary>
        /// The keyword ID this report is for
        /// </summary>
        public string KeywordId { get; set; } = string.Empty;

        /// <summary>
        /// The job ID this report is for
        /// </summary>
        public string JobId { get; set; } = string.Empty;

        /// <summary>
        /// The keyword alias for display purposes
        /// </summary>
        public string KeywordAlias { get; set; } = string.Empty;

        /// <summary>
        /// All transactions for this keyword/job combination
        /// </summary>
        public List<PurchaseTransaction> Transactions { get; set; } = new List<PurchaseTransaction>();

        /// <summary>
        /// All purchase attempts for this keyword/job combination
        /// </summary>
        public List<PurchaseAttempt> Attempts { get; set; } = new List<PurchaseAttempt>();

        /// <summary>
        /// When this report was generated
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Total quantity purchased across all transactions
        /// </summary>
        public int TotalQuantityPurchased => Transactions?.Sum(t => t.Quantity) ?? 0;

        /// <summary>
        /// Total amount spent across all transactions
        /// </summary>
        public decimal TotalAmountSpent => Transactions?.Sum(t => t.PurchasePrice * t.Quantity) ?? 0;

        /// <summary>
        /// Number of successful purchase attempts
        /// </summary>
        public int SuccessfulAttempts => Attempts?.Count(a => a.Result == "Success") ?? 0;

        /// <summary>
        /// Number of failed purchase attempts
        /// </summary>
        public int FailedAttempts => Attempts?.Count(a => a.Result == "Failed") ?? 0;

        /// <summary>
        /// Most recent transaction status
        /// </summary>
        public string LastTransactionStatus => Transactions?.OrderByDescending(t => t.PurchaseDate).FirstOrDefault()?.Status ?? "None";

        /// <summary>
        /// Most recent attempt result
        /// </summary>
        public string LastAttemptResult => Attempts?.OrderByDescending(a => a.AttemptDate).FirstOrDefault()?.Result ?? "None";
    }
}
