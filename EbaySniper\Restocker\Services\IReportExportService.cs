using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Interface for exporting reports to various formats
    /// </summary>
    public interface IReportExportService
    {
        /// <summary>
        /// Exports user reports to CSV format
        /// </summary>
        /// <param name="reports">The reports to export</param>
        /// <param name="filePath">The file path to save the CSV</param>
        /// <returns>Task representing the async operation</returns>
        Task ExportUserReportToCsvAsync(IEnumerable<UserReport> reports, string filePath);

        /// <summary>
        /// Exports a transaction detail report to CSV format
        /// </summary>
        /// <param name="report">The report to export</param>
        /// <param name="filePath">The file path to save the CSV</param>
        /// <returns>Task representing the async operation</returns>
        Task ExportTransactionDetailReportToCsvAsync(TransactionDetailReport report, string filePath);

        /// <summary>
        /// Saves HTML content from a purchase step to a file
        /// </summary>
        /// <param name="htmlContent">The HTML content to save</param>
        /// <param name="keywordId">The keyword ID for file naming</param>
        /// <param name="jobId">The job ID for file naming</param>
        /// <param name="timestamp">The timestamp for file naming</param>
        /// <returns>The file path where the HTML was saved</returns>
        Task<string> SaveHtmlContentAsync(string htmlContent, string keywordId, string jobId, DateTime timestamp);

        /// <summary>
        /// Gets the directory path where HTML files are stored
        /// </summary>
        /// <returns>The HTML storage directory path</returns>
        string GetHtmlStorageDirectory();

        /// <summary>
        /// Cleans up old HTML files based on retention policy
        /// </summary>
        /// <param name="retentionDays">Number of days to retain files</param>
        /// <returns>Number of files deleted</returns>
        Task<int> CleanupOldHtmlFilesAsync(int retentionDays = 30);
    }
}
