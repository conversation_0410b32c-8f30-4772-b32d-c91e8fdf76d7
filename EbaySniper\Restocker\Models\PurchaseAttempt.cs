using System;

namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents an attempt to purchase an item (successful or failed)
    /// </summary>
    public class PurchaseAttempt
    {
        public int Id { get; set; }

        /// <summary>
        /// References the keyword's unique identifier from Keyword2Find.Id
        /// </summary>
        public string KeywordId { get; set; } = string.Empty;

        /// <summary>
        /// Job ID for grouping related purchase attempts
        /// </summary>
        public string JobId { get; set; } = string.Empty;

        /// <summary>
        /// eBay item ID that was attempted to be purchased
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// When the purchase attempt was made
        /// </summary>
        public DateTime AttemptDate { get; set; }

        /// <summary>
        /// Result of the attempt: "Success", "Failed", "Skipped"
        /// </summary>
        public string Result { get; set; } = string.Empty;

        /// <summary>
        /// Error message if the attempt failed
        /// </summary>
        public string ErrorMessage { get; set; }

        public PurchaseAttempt()
        {
            AttemptDate = DateTime.UtcNow;
        }
    }
}
