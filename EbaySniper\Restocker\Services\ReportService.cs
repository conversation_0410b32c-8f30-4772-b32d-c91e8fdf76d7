using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service for generating reports from Restocker data
    /// </summary>
    public class ReportService : IReportService
    {
        private readonly IPurchaseTrackerRepository _repository;

        public ReportService(IPurchaseTrackerRepository repository)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        }

        /// <summary>
        /// Generates a detailed transaction report for a specific keyword and job
        /// </summary>
        public async Task<TransactionDetailReport> GenerateTransactionDetailReportAsync(string keywordId, string jobId)
        {
            if (string.IsNullOrEmpty(keywordId))
                throw new ArgumentNullException(nameof(keywordId));
            
            if (string.IsNullOrEmpty(jobId))
                throw new ArgumentNullException(nameof(jobId));

            var transactions = await _repository.GetTransactionsByKeywordAndJobAsync(keywordId, jobId);
            var attempts = await _repository.GetAttemptsByKeywordAndJobAsync(keywordId, jobId);

            // Get keyword alias from the first transaction or attempt
            var keywordAlias = transactions.FirstOrDefault()?.KeywordId ?? 
                              attempts.FirstOrDefault()?.KeywordId ?? 
                              keywordId;

            return new TransactionDetailReport
            {
                KeywordId = keywordId,
                JobId = jobId,
                KeywordAlias = keywordAlias,
                Transactions = transactions.ToList(),
                Attempts = attempts.ToList()
            };
        }

        /// <summary>
        /// Generates user reports based on the provided filter
        /// </summary>
        public async Task<IEnumerable<UserReport>> GenerateUserReportAsync(ReportFilter filter)
        {
            if (filter == null)
                throw new ArgumentNullException(nameof(filter));

            if (!filter.IsValid())
                throw new ArgumentException("Invalid filter criteria", nameof(filter));

            IEnumerable<PurchaseTransaction> transactions;

            // Get transactions based on filter criteria
            if (filter.StartDate.HasValue && filter.EndDate.HasValue)
            {
                transactions = await _repository.GetTransactionsByDateRangeAsync(filter.StartDate.Value, filter.EndDate.Value);
            }
            else
            {
                transactions = await _repository.GetAllTransactionsAsync();
            }

            // Apply additional filters
            transactions = ApplyFilters(transactions, filter);

            // Group by KeywordId and JobId to create user reports
            var groupedTransactions = transactions
                .GroupBy(t => new { t.KeywordId, t.JobId })
                .ToList();

            var userReports = new List<UserReport>();

            foreach (var group in groupedTransactions)
            {
                var keywordId = group.Key.KeywordId;
                var jobId = group.Key.JobId;
                
                // Get the most recent transaction for this group
                var lastTransaction = group.OrderByDescending(t => t.PurchaseDate).First();
                
                // Get keyword data (this would need to be implemented in repository)
                var keyword = await GetKeywordDataAsync(keywordId);
                
                var userReport = new UserReport();
                
                if (keyword != null)
                {
                    userReport.PopulateFromKeyword(keyword);
                }
                else
                {
                    // Fallback if keyword data not available
                    userReport.KeywordId = keywordId;
                    userReport.JobId = jobId;
                    userReport.KeywordAlias = keywordId;
                }

                userReport.UpdateFromLastTransaction(lastTransaction);
                
                // Calculate purchased quantity for this job
                userReport.PurchasedQuantity = group.Where(t => t.Status == "Completed").Sum(t => t.Quantity);

                userReports.Add(userReport);
            }

            // Apply max records limit
            if (filter.MaxRecords > 0)
            {
                userReports = userReports.Take(filter.MaxRecords).ToList();
            }

            return userReports;
        }

        /// <summary>
        /// Gets summary statistics for all purchase activities
        /// </summary>
        public async Task<ReportSummary> GetReportSummaryAsync(ReportFilter filter = null)
        {
            IEnumerable<PurchaseTransaction> transactions;

            if (filter?.StartDate.HasValue == true && filter.EndDate.HasValue)
            {
                transactions = await _repository.GetTransactionsByDateRangeAsync(filter.StartDate.Value, filter.EndDate.Value);
            }
            else
            {
                transactions = await _repository.GetAllTransactionsAsync();
            }

            if (filter != null)
            {
                transactions = ApplyFilters(transactions, filter);
            }

            var transactionList = transactions.ToList();

            return new ReportSummary
            {
                TotalTransactions = transactionList.Count,
                CompletedTransactions = transactionList.Count(t => t.Status == "Completed"),
                FailedTransactions = transactionList.Count(t => t.Status == "Failed"),
                PendingTransactions = transactionList.Count(t => t.Status == "Pending"),
                TotalAmountSpent = transactionList.Where(t => t.Status == "Completed").Sum(t => t.PurchasePrice * t.Quantity),
                TotalQuantityPurchased = transactionList.Where(t => t.Status == "Completed").Sum(t => t.Quantity),
                UniqueJobIds = transactionList.Select(t => t.JobId).Distinct().Count(),
                UniqueKeywordIds = transactionList.Select(t => t.KeywordId).Distinct().Count()
            };
        }

        private IEnumerable<PurchaseTransaction> ApplyFilters(IEnumerable<PurchaseTransaction> transactions, ReportFilter filter)
        {
            var filtered = transactions.AsQueryable();

            if (!string.IsNullOrEmpty(filter.JobId))
            {
                filtered = filtered.Where(t => t.JobId == filter.JobId);
            }

            if (!string.IsNullOrEmpty(filter.KeywordId))
            {
                filtered = filtered.Where(t => t.KeywordId == filter.KeywordId);
            }

            var statusFilters = filter.GetStatusFilters();
            filtered = filtered.Where(t => statusFilters.Contains(t.Status));

            return filtered.ToList();
        }

        private async Task<Keyword2Find> GetKeywordDataAsync(string keywordId)
        {
            // This would need to be implemented to get keyword data
            // For now, return null and handle in the calling method
            // TODO: Implement keyword data retrieval
            await Task.CompletedTask;
            return null;
        }
    }
}
