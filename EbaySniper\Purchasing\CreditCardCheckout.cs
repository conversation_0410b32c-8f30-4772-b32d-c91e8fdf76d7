﻿using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Purchasing.Cookies;

namespace uBuyFirst.Purchasing
{
    public static class CreditCardCheckout
    {
        public static async Task ExecuteCreditCardCheckout(DataList d, int quantityToPurchase)
        {
            var quantity = quantityToPurchase;

            if (d.Order is not { OrderAction: Placeoffer.OrderAction.PayWithCreditCard })
            {
                var effectivePurchasePrice = d.ItemPricing.GetEffectivePurchasePrice();
                d.Order = new BuyingService.BuyOrder(d.ItemID, d.Title, d.EBaySite, quantity, effectivePurchasePrice, Placeoffer.OrderAction.PayWithCreditCard);
            }

            PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies started");
            d.Order.CookieContainer = CookieManager.ReadCookiesFirefox(new[] { $".{d.Order.EbaySite.Domain}" });
            PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies stopped");
            switch (d.Order.CheckoutStatus)
            {
                case BuyingService.Order.CheckoutState.NotStarted:
                    await CreditCardService.CreditCartCheckoutCompleteSequence(d);

                    // Check the final status on the order object after the sequence completes
                    ShowUserMessageAfterPurchaseAttempt(d);

                    break;
                case BuyingService.Order.CheckoutState.CreatingSession:
                    d.Order.AutoConfirmationAllowed = true;

                    break;

                case BuyingService.Order.CheckoutState.SessionCreated:
                    d.SetStatus(ItemStatus.PaymentInProgress);
                    d.Order.AutoConfirmationAllowed = true;
                    // This case handles resuming after session creation. The confirmation call itself now sets the status.
                    // We need a similar ContinueWith block here to show the flyout after confirmation finishes.
                    await CreditCardService.ConfirmCreditCardPayment((BuyingService.BuyOrder)d.Order, d.Title);
                    ShowUserMessageAfterPurchaseAttempt(d);

                    break;
            }
        }

        private static void ShowUserMessageAfterPurchaseAttempt(DataList d)
        {
            // Check the final status on the order object after the confirmation completes
            if (d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess
                || d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
            {
                // Instantiate FlyoutPanelSnackBar directly
                string successMessage;
                if (d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
                {
                    successMessage = $"Test purchase successful for {d.Title}.";
                    d.SetStatus(ItemStatus.TestPurchase);
                }
                else
                {
                    successMessage = $"Credit card payment successful for {d.Title}.";
                    d.SetStatus(ItemStatus.Active); // Or ItemStatus.Sold? Set appropriate status on success
                }

                if (d.GridControl != null)
                {
                    var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);

                    flyoutSnackBar.ShowSuccess(d.GridControl, successMessage);
                    // FlyoutPanelSnackBar handles its own disposal
                }
            }
            else // Handle various failure states (PaymentFailed, ConfirmationFailed, etc.)
            {
                var failureMessage = d.Order.FailureReasonMessage ?? "Credit card payment failed (Unknown reason).";
                // Instantiate FlyoutPanelSnackBar directly
                if (d.GridControl != null)
                {
                    var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                    flyoutSnackBar.ShowFailure(d.GridControl, $"Credit card payment failed for {d.Title}: {failureMessage}");
                    // FlyoutPanelSnackBar handles its own disposal
                }
            }
        }
    }
}
