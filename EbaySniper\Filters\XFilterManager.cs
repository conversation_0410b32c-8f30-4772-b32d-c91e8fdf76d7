﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DevExpress.Data.Extensions;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.BandedGrid;
using DevExpress.XtraGrid.Views.Grid;
using Lucene.Net.Util;
using uBuyFirst.AI;
using uBuyFirst.Data;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst.Filters
{
    internal static class XFilterManager
    {
        private static BindingList<XFilterClass> _xFiltersList = new();

        // Event for logging filter removals
        public static event Action<string, string, string, string> FilterRemovalLogged;

        /// <summary>
        /// Triggers the filter removal logging event
        /// </summary>
        /// <param name="itemId">The item ID that was removed</param>
        /// <param name="filterAlias">The alias of the filter that triggered the removal</param>
        /// <param name="title">The title of the item that was removed</param>
        /// <param name="timestamp">The timestamp of the removal</param>
        private static void LogFilterRemoval(string itemId, string filterAlias, string title, string timestamp)
        {
            FilterRemovalLogged?.Invoke(itemId, filterAlias, title, timestamp);
        }

        public static void AddFilter(XFilterClass xFilter)
        {
            var uniqueAliasesDict = Helpers.CountStrings(_xFiltersList.Select(filter => filter.Alias).ToList());
            xFilter.Alias = Helpers.MakeUniqAliasOnAdd(xFilter.Alias, uniqueAliasesDict);
            _xFiltersList.Add(xFilter);
        }

        public static void UpdateFilter(int index, XFilterClass xFilter)
        {
            var uniqueAliasesDict = Helpers.CountStrings(_xFiltersList.Select(filter => filter.Alias).ToList());
            xFilter.Alias = Tools.Helpers.MakeUniqAliasOnEdit(xFilter.Alias, uniqueAliasesDict);
            _xFiltersList[index] = xFilter;
        }

        public static XFilterClass DuplicateFilter(XFilterClass xFilter)
        {
            var xFilterString = Serializator.SerializeToString(xFilter);
            var gridViewColumn = ((AdvBandedGridView)ResultsView.ViewsDict["Results"].MainView)?.Columns[xFilter.FormatColumn];

            var xFilterCopy = Serializator.DeSerializeFromString<XFilterClass>(xFilterString);
            xFilterCopy.Id = Guid.NewGuid();
            xFilterCopy.GridFormatRule = xFilterCopy.DeSerializeGridFormatRule(gridViewColumn);
            xFilterCopy.Rebuild();
            xFilterCopy.Enabled = false;

            AddFilter(xFilterCopy);
            return xFilterCopy;
        }

        internal static void Remove(XFilterClass xFilter)
        {
            _xFiltersList.Remove(xFilter);
        }

        public static void MoveFilter(int prevIndex, int selectedIndex)
        {
            var item = _xFiltersList[prevIndex];
            _xFiltersList.RemoveAt(prevIndex);
            if (selectedIndex == -1)
                AddFilter(item);
            else
                _xFiltersList.Insert(selectedIndex, item);
        }

        public static int? FilterCount()
        {
            return _xFiltersList.Count;
        }

        public static BindingList<XFilterClass> GetDataSource()
        {
            return _xFiltersList;
        }

        public static void DeserializeXfilters(string xFiltersListStr)
        {
            var importErrorMessages = new List<string>();
            _xFiltersList = Serializator.DeSerializeFromString<BindingList<XFilterClass>>(xFiltersListStr);
            foreach (var xFilter in _xFiltersList)
            {
                xFilter.EnsureModernActionFormat(); // Ensure modern format after deserialization

                if (xFilter.Id == Guid.Empty)
                {
                    xFilter.Id = Guid.NewGuid();
                }

                var gridViewColumn = ((AdvBandedGridView)ResultsView.ViewsDict["Results"].MainView)?.Columns[xFilter.FormatColumn];
                xFilter.GridFormatRule = xFilter.DeSerializeGridFormatRule(gridViewColumn);
                xFilter.Rebuild();

                if (gridViewColumn != null)
                    continue;

                xFilter.Enabled = false;
                importErrorMessages.Add($"Column \'{xFilter.FormatColumn}\' not found.           \r\nFilter \'{xFilter.Alias}\' was disabled.");
            }

            if (importErrorMessages.Any())
            {
                XtraMessageBox.Show(string.Join("\r\n", importErrorMessages), "Import Error");
            }
        }

        public static string GetSerializedDatasource()
        {
            return Serializator.SerializeToString(_xFiltersList);
        }

        public static string ApplyRemoveRowFilter(DataRow row)
        {
            foreach (var xfilter in _xFiltersList.Where(f => f.Enabled))
            {
                // Check both new and legacy action systems
                if ((xfilter.ActionHandler is RemoveRowsAction) ||
                    (xfilter.Action == "Remove rows" && xfilter.ActionHandler == null))
                {
                    try
                    {
                        var isFit = xfilter.GetEvaluator().Fit(row);
                        if (isFit)
                        {
                            return xfilter.Alias;
                        }
                    }
                    catch (System.Exception ex)
                    {
                        //throw;
                    }
                }
            }

            return "";
        }

        public static string IsMatchForTelegram(DataRow row)
        {
            // Use the new action system if available
            var telegramFilters = _xFiltersList
                .Where(f => f.Enabled &&
                    ((f.ActionHandler is SendToTelegramAction) ||
                     (f.Action == "Send to Telegram" && f.ActionHandler == null)))
                .ToArray();

            if (!telegramFilters.Any())
            {
                return "No telegram filters";
            }

            foreach (var xfilter in telegramFilters)
            {
                try
                {
                    var isFit = xfilter.GetEvaluator().Fit(row);
                    if (isFit)
                    {
                        return xfilter.Alias;
                    }
                }
                catch (System.Exception ex)
                {
                    //throw;
                }
            }

            return "";
        }

        /// <summary>
        /// Checks if any Restock filters match the given row and triggers purchase attempts
        /// </summary>
        public static async Task<string> ProcessRestockFiltersAsync(DataRow row)
        {
            // Debug logging to understand what's happening
            var allRestockFilters = _xFiltersList.Where(f => f.Action == "Restock").ToArray();
            Debug.WriteLine($"ProcessRestockFiltersAsync: Found {allRestockFilters.Length} Restock filters total");

            foreach (var filter in allRestockFilters)
            {
                Debug.WriteLine($"  Filter '{filter.Alias}': Enabled={filter.Enabled}, Action='{filter.Action}', ActionIdentifier='{filter.ActionIdentifier}', ActionHandler={filter.ActionHandler?.GetType().Name ?? "null"}");

                // Try to fix null ActionHandler on the spot
                if (filter.ActionHandler == null && filter.Action == "Restock")
                {
                    Debug.WriteLine($"  Attempting to fix null ActionHandler for filter '{filter.Alias}'");
                    filter.EnsureModernActionFormat();
                    Debug.WriteLine($"  After fix attempt: ActionHandler={filter.ActionHandler?.GetType().Name ?? "null"}");
                }
            }

            var restockFilters = _xFiltersList
                .Where(f => f.Enabled &&
                    ((f.ActionHandler is RestockFilterAction) ||
                     (f.Action == "Restock" && f.ActionHandler == null)))
                .ToArray();

            Debug.WriteLine($"ProcessRestockFiltersAsync: {restockFilters.Length} enabled Restock filters found");

            if (!restockFilters.Any())
            {
                return "";
            }

            var matchedFilters = new List<string>();

            foreach (var xfilter in restockFilters)
            {
                try
                {
                    var isFit = xfilter.GetEvaluator().Fit(row);
                    if (isFit)
                    {
                        if (xfilter != null)
                        {
                            matchedFilters.Add(xfilter.Alias);

                            // Execute the Restock action if using the new action system
                            if (xfilter.ActionHandler is RestockFilterAction restockAction)
                            {
                                var context = new FilterActionContext
                                {
                                    FilterRule = xfilter,
                                    CurrentRow = row,
                                    SourceDataTable = row.Table
                                };

                                var result = await restockAction.ExecuteAsync(context).ConfigureAwait(false);

                                // Log the result if needed
                                if (!result.Success)
                                {
                                    Console.WriteLine($"Restock filter '{xfilter.Alias}' failed: {result.Message}");
                                }
                            }
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    Console.WriteLine($"Error processing Restock filter '{xfilter.Alias}': {ex.Message}");
                }
            }

            return string.Join(", ", matchedFilters);
        }

        public static async Task ApplyFiltersAsync(GridView grView, DataTable dataTable)
        {
            foreach (var xFilter in _xFiltersList)
            {
                await ApplyFilterAsync(grView, xFilter, dataTable);
            }
        }

        // Synchronous version for backward compatibility
        public static void ApplyFilters(GridView grView, DataTable dataTable)
        {
            ApplyFiltersAsync(grView, dataTable).GetAwaiter().GetResult();
        }

        // Synchronous version for backward compatibility
        public static void ApplyFilter(GridView grView, XFilterClass xFilter, DataTable dataTable)
        {
            ApplyFilterAsync(grView, xFilter, dataTable).GetAwaiter().GetResult();
        }

        public static async Task ApplyFilterAsync(GridView grView, XFilterClass xFilter, DataTable dataTable)
        {
            if (xFilter?.ActionHandler == null || !xFilter.Enabled)
            {
                // Fallback to legacy behavior if no action handler
                if (!string.IsNullOrEmpty(xFilter?.Action))
                {
                    ApplyLegacyFilter(grView, xFilter, dataTable);
                }
                return;
            }

            try
            {
                var gridViewColumn = grView.Columns[xFilter.FormatColumn];
                xFilter.GridFormatRule = xFilter.DeSerializeGridFormatRule(gridViewColumn);
                xFilter.Rebuild();

                var context = new FilterActionContext
                {
                    FilterRule = xFilter,
                    GridView = grView,
                    SourceDataTable = dataTable
                };

                var result = await xFilter.ActionHandler.ExecuteAsync(context);

                if (!result.Success)
                {
                    // Log error or handle failure
                    Console.WriteLine($"Filter action failed: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                // Handle execution error
                Console.WriteLine($"Filter execution error: {ex.Message}");
            }
        }

        private static void ApplyLegacyFilter(GridView grView, XFilterClass xFilter, DataTable dataTable)
        {
            var gridViewColumn = grView.Columns[xFilter.FormatColumn];
            xFilter.GridFormatRule = xFilter.DeSerializeGridFormatRule(gridViewColumn);

            xFilter.Rebuild();

            ApplyRemoveRowsFilter(xFilter, dataTable);
            ApplyBuyWithFilter(xFilter, dataTable);

            if (xFilter.Action.Contains("Format rows") || xFilter.Action.Contains("Format cells"))
            {
                grView.FormatRules.Add(xFilter.GridFormatRule);
            }
        }
        public static void AddOrReplaceFilter(GridView grView, XFilterClass xFilter, DataTable dataTable)
        {
            if (string.IsNullOrEmpty(xFilter.Action))
                return;

            var gridViewColumn = grView.Columns[xFilter.FormatColumn];
            xFilter.GridFormatRule = xFilter.DeSerializeGridFormatRule(gridViewColumn);

            xFilter.Rebuild();

            ApplyRemoveRowsFilter(xFilter, dataTable);
            ApplyBuyWithFilter(xFilter, dataTable);

            // Check if the action is to format rows
            if (xFilter.Action.Contains("Format rows") || xFilter.Action.Contains("Format cells"))
            {
                // Find the existing rule by identifier and its index
                var existingRuleIndex = grView.FormatRules
                    .FindIndex(fr => (fr.Tag is Guid tag ? tag : default) == xFilter.Id);

                if (existingRuleIndex >= 0)
                {
                    // Remove the existing rule and re-insert the new rule at the same index
                    grView.FormatRules.RemoveAt(existingRuleIndex);
                    xFilter.GridFormatRule.Tag = xFilter.Id;  // Set identifier for future reference
                    grView.FormatRules.Insert(existingRuleIndex, xFilter.GridFormatRule);
                }
                else
                {
                    // Add as a new rule if no matching rule is found
                    xFilter.GridFormatRule.Tag = xFilter.Id;
                    grView.FormatRules.Add(xFilter.GridFormatRule);
                }
            }
        }

        private static void ApplyBuyWithFilter(XFilterClass filter, DataTable dataTable)
        {
            if (!filter.Action.Contains("Buy with") || !filter.Enabled)
                return;

            var eBayAccount = Form1.EBayAccountsList.FirstOrDefault(account => filter.Action.Contains(account.UserName));

            if (eBayAccount == null)
                return;

            var i = 0;
            while (dataTable.Rows.Count > i)
            {
                var row = dataTable.Rows[i];
                if (filter.GetEvaluator().Fit(row))
                {
                    var d = (DataList)row["Blob"];
                    d.EbayAccount = eBayAccount;
                    row["Blob"] = d;
                }

                i++;
            }
        }

        private static void ApplyRemoveRowsFilter(XFilterClass filter, DataTable dataTable)
        {
            if (filter.Action == "Remove rows" && filter.Enabled)
            {
                var i = 0;
                while (dataTable.Rows.Count > i)
                {
                    if (filter.GetEvaluator().Fit(dataTable.Rows[i]))
                    {
                        dataTable.Rows[i].Delete();
                    }
                    else
                    {
                        i++;
                    }
                }
            }
        }

        public static List<List<string>> ExportToCellLists()
        {
            var allFiltersCells = new List<List<string>>();
            foreach (var xFilter in _xFiltersList)
            {
                allFiltersCells.Add(xFilter.Export()); // xFilter.Export() now returns List<string>
            }
            return allFiltersCells;
        }

        // Keep a similar method for string export if legacy parts of the app still expect it,
        // or refactor those parts as well. For now, let's assume BtnExportFilters_Click is the main consumer.
        // If direct string export is still needed elsewhere:
        public static string ExportToString()
        {
            var sb = new StringBuilder();
            foreach (var filterCells in ExportToCellLists())
            {
                sb.Append(Helpers.CreateCSVRow(filterCells));
                sb.Append("\r\n");
            }
            return sb.ToString();
        }

        public static bool ContainsFilter(string alias)
        {
            return _xFiltersList.Any(a => a.Alias == alias);
        }

        public static void SortFilters()
        {
            _xFiltersList.Sort();
        }

        public static List<string> ExtractColumns()
        {
            var filters = _xFiltersList.Select(f => f.FilterCriteria);
            return CriteriaColumnExtractor.ExtractColumns(filters);
        }

        /// <summary>
        /// Applies only non-AI filters to a row (first pass filtering)
        /// </summary>
        /// <param name="row">DataRow to filter</param>
        /// <returns>Filter alias if row should be removed, empty string otherwise</returns>
        public static string ApplyNonAiRemoveRowFilter(DataRow row)
        {
            foreach (var xfilter in _xFiltersList)
            {
                if (xfilter.Action == "Remove rows" && xfilter.Enabled)
                {
                    try
                    {
                        // Check if this filter uses AI columns
                        if (FilterUsesAiColumns(xfilter))
                        {
                            continue; // Skip AI filters in first pass
                        }

                        var isFit = xfilter.GetEvaluator().Fit(row);
                        if (isFit)
                        {
                            // Log the removal
                            try
                            {
                                var localDateTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, UserSettings.CurrentTimeZoneInfo);
                                var localDateTimeStr = localDateTime.ToString("s").Replace("T", " ");
                                LogFilterRemoval(row["ItemID"]?.ToString() ?? "", xfilter.Alias, row["Title"]?.ToString() ?? "", localDateTimeStr);
                            }
                            catch (Exception logEx)
                            {
                                Debug.WriteLine($"Error logging filter removal: {logEx.Message}");
                            }

                            return xfilter.Alias;
                        }
                    }
                    catch (System.Exception ex)
                    {
                        Debug.WriteLine($"Error in non-AI filter '{xfilter.Alias}': {ex.Message}");
                    }
                }
            }

            return "";
        }

        /// <summary>
        /// Applies only AI filters to a row (second pass filtering)
        /// </summary>
        /// <param name="row">DataRow to filter</param>
        /// <returns>Filter alias if row should be removed, empty string otherwise</returns>
        public static string ApplyAiRemoveRowFilter(DataRow row)
        {
            foreach (var xfilter in _xFiltersList)
            {
                if (xfilter.Action == "Remove rows" && xfilter.Enabled)
                {
                    try
                    {
                        // Check if this filter uses AI columns
                        if (!FilterUsesAiColumns(xfilter))
                        {
                            continue; // Skip non-AI filters in second pass
                        }

                        var isFit = xfilter.GetEvaluator().Fit(row);
                        if (isFit)
                        {
                            // Log the removal
                            try
                            {
                                var localDateTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, UserSettings.CurrentTimeZoneInfo);
                                var localDateTimeStr = localDateTime.ToString("s").Replace("T", " ");
                                LogFilterRemoval(row["ItemID"]?.ToString() ?? "", xfilter.Alias, row["Title"]?.ToString() ?? "", localDateTimeStr);
                            }
                            catch (Exception logEx)
                            {
                                Debug.WriteLine($"Error logging filter removal: {logEx.Message}");
                            }

                            return xfilter.Alias;
                        }
                    }
                    catch (System.Exception ex)
                    {
                        Debug.WriteLine($"Error in AI filter '{xfilter.Alias}': {ex.Message}");
                    }
                }
            }

            return "";
        }

        /// <summary>
        /// Determines if a filter uses any AI columns
        /// </summary>
        /// <param name="filter">The filter to check</param>
        /// <returns>True if the filter uses AI columns, false otherwise</returns>
        private static bool FilterUsesAiColumns(XFilterClass filter)
        {
            if (filter.FilterCriteria == null)
                return false;

            try
            {
                var columnsUsed = CriteriaColumnExtractor.ExtractColumns(new[] { filter.FilterCriteria });
                return columnsUsed.Any(column => column.StartsWith(AiAnalysis.AiPrefix, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking AI columns for filter '{filter.Alias}': {ex.Message}");
                return false; // If we can't determine, treat as non-AI filter
            }
        }

        /// <summary>
        /// Checks if there are any enabled AI filters in the system
        /// </summary>
        /// <returns>True if there are enabled AI filters, false otherwise</returns>
        public static bool HasEnabledAiFilters()
        {
            return _xFiltersList.Any(filter =>
                filter.Action == "Remove rows" &&
                filter.Enabled &&
                FilterUsesAiColumns(filter));
        }
        public static void ClearFilters()
        {
            _xFiltersList.Clear();
        }
    }
}
