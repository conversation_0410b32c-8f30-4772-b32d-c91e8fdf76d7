using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class ReportServiceTests
    {
        private Mock<IPurchaseTrackerRepository> _mockRepository;
        private ReportService _reportService;

        [TestInitialize]
        public void Setup()
        {
            _mockRepository = new Mock<IPurchaseTrackerRepository>();
            _reportService = new ReportService(_mockRepository.Object);
        }

        [TestMethod]
        public async Task GenerateTransactionDetailReportAsync_WithValidKeywordAndJob_ReturnsDetailedReport()
        {
            // Arrange
            var keywordId = "test-keyword";
            var jobId = "job-123";
            
            var transactions = new List<PurchaseTransaction>
            {
                new PurchaseTransaction
                {
                    Id = 1,
                    KeywordId = keywordId,
                    JobId = jobId,
                    ItemId = "item-456",
                    Status = "Completed",
                    Quantity = 2,
                    PurchasePrice = 25.99m,
                    LastStepHtml = "<html>Success</html>"
                }
            };

            var attempts = new List<PurchaseAttempt>
            {
                new PurchaseAttempt
                {
                    Id = 1,
                    KeywordId = keywordId,
                    JobId = jobId,
                    ItemId = "item-456",
                    Result = "Success"
                }
            };

            _mockRepository.Setup(r => r.GetTransactionsByKeywordAndJobAsync(keywordId, jobId))
                          .ReturnsAsync(transactions);
            _mockRepository.Setup(r => r.GetAttemptsByKeywordAndJobAsync(keywordId, jobId))
                          .ReturnsAsync(attempts);

            // Act
            var result = await _reportService.GenerateTransactionDetailReportAsync(keywordId, jobId);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(keywordId, result.KeywordId);
            Assert.AreEqual(jobId, result.JobId);
            Assert.AreEqual(1, result.Transactions.Count);
            Assert.AreEqual(1, result.Attempts.Count);
            Assert.AreEqual("Completed", result.Transactions.First().Status);
            Assert.AreEqual("<html>Success</html>", result.Transactions.First().LastStepHtml);
        }

        [TestMethod]
        public async Task GenerateUserReportAsync_WithDateRange_ReturnsFilteredResults()
        {
            // Arrange
            var filter = new ReportFilter
            {
                StartDate = DateTime.Now.AddDays(-7),
                EndDate = DateTime.Now
            };

            var transactions = new List<PurchaseTransaction>
            {
                new PurchaseTransaction
                {
                    Id = 1,
                    KeywordId = "keyword-1",
                    JobId = "job-123",
                    Status = "Completed",
                    PurchaseDate = DateTime.Now.AddDays(-3),
                    LastStepHtml = "<html>Success</html>"
                }
            };

            _mockRepository.Setup(r => r.GetTransactionsByDateRangeAsync(filter.StartDate.Value, filter.EndDate.Value))
                          .ReturnsAsync(transactions);

            // Act
            var result = await _reportService.GenerateUserReportAsync(filter);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Count());
            var report = result.First();
            Assert.AreEqual("keyword-1", report.KeywordId);
            Assert.AreEqual("job-123", report.JobId);
            Assert.AreEqual("Completed", report.LastOrderStatus);
        }

        [TestMethod]
        public async Task GenerateUserReportAsync_WithJobIdFilter_ReturnsMatchingResults()
        {
            // Arrange
            var filter = new ReportFilter
            {
                JobId = "job-123"
            };

            var transactions = new List<PurchaseTransaction>
            {
                new PurchaseTransaction
                {
                    Id = 1,
                    KeywordId = "keyword-1",
                    JobId = "job-123",
                    Status = "Completed"
                },
                new PurchaseTransaction
                {
                    Id = 2,
                    KeywordId = "keyword-2",
                    JobId = "job-456",
                    Status = "Failed"
                }
            };

            _mockRepository.Setup(r => r.GetAllTransactionsAsync())
                          .ReturnsAsync(transactions);

            // Act
            var result = await _reportService.GenerateUserReportAsync(filter);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Count());
            Assert.AreEqual("job-123", result.First().JobId);
        }

        [TestMethod]
        public async Task GenerateUserReportAsync_WithStatusFilter_ReturnsFilteredResults()
        {
            // Arrange
            var filter = new ReportFilter
            {
                IncludeCompleted = true,
                IncludeFailed = false,
                IncludePending = false
            };

            var transactions = new List<PurchaseTransaction>
            {
                new PurchaseTransaction { Status = "Completed", KeywordId = "k1", JobId = "j1" },
                new PurchaseTransaction { Status = "Failed", KeywordId = "k2", JobId = "j2" },
                new PurchaseTransaction { Status = "Pending", KeywordId = "k3", JobId = "j3" }
            };

            _mockRepository.Setup(r => r.GetAllTransactionsAsync())
                          .ReturnsAsync(transactions);

            // Act
            var result = await _reportService.GenerateUserReportAsync(filter);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Count());
            Assert.AreEqual("Completed", result.First().LastOrderStatus);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public async Task GenerateTransactionDetailReportAsync_WithNullKeywordId_ThrowsException()
        {
            // Act
            await _reportService.GenerateTransactionDetailReportAsync(null, "job-123");
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public async Task GenerateTransactionDetailReportAsync_WithNullJobId_ThrowsException()
        {
            // Act
            await _reportService.GenerateTransactionDetailReportAsync("keyword-123", null);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public async Task GenerateUserReportAsync_WithNullFilter_ThrowsException()
        {
            // Act
            await _reportService.GenerateUserReportAsync(null);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public async Task GenerateUserReportAsync_WithInvalidFilter_ThrowsException()
        {
            // Arrange
            var filter = new ReportFilter
            {
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(-1) // Invalid: end before start
            };

            // Act
            await _reportService.GenerateUserReportAsync(filter);
        }
    }
}
