﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using BrowseAPI;
using DevExpress.XtraBars.Alerter;
using DevExpress.XtraBars.Docking;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.BandedGrid;
using eBay.Service.Core.Soap;
using uBuyFirst.API.ShoppingAPI;
using uBuyFirst.BrowseAPI;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.Grid;
using uBuyFirst.GUI;
using uBuyFirst.Images;
using uBuyFirst.Intl;
using uBuyFirst.Item;
using uBuyFirst.Network;
using uBuyFirst.Parsing;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Search;
using uBuyFirst.Search.Status;
using uBuyFirst.Seller;
using uBuyFirst.Time;
using uBuyFirst.Tools;
using uBuyFirst.Views;
using uBuyFirst.Watchlist;
using uBuyFirst.AI;

namespace uBuyFirst
{
    public partial class Form1
    {

        private static readonly Dictionary<string, byte> ProcessingItems = new();

        private async void HandleNewItem(FoundItem foundItem)
        {
            while (ProcessingItems.ContainsKey(foundItem.ItemID))
            {
                await Task.Delay(200);
            }

            ProcessingItems.Add(foundItem.ItemID, 0);

            try
            {
                var reason = await HandleNewItemFromRssAsync(foundItem).ConfigureAwait(true);
                Interlocked.Increment(ref Stat.TotalItemsProcessed);
                if (string.IsNullOrEmpty(reason.Item1))
                {
                }
                else
                {
                    Debug.WriteLine(reason);
                    reason.Item2?.CancelEdit();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            ProcessingItems.Remove(foundItem.ItemID);

        }

        private async Task<Tuple<string, DataRow?>> HandleNewItemFromRssAsync(FoundItem foundItem)
        {
            DataRow? row = null;
            try
            {
                var keyword2Find = foundItem.Keyword2Find;
                var termAlias = "Watchlist";
                var siteCodeType = CountryProvider.GetEbaySite("eBay US");
                var zip = "90210";
                var availableTo = "US";
                var gridControl = WatchlistManager.WatchlistGridControl;
                var dataTable = (DataTable)gridControl.DataSource;
                if (keyword2Find != null)
                {
                    termAlias = keyword2Find.Alias;
                    siteCodeType = keyword2Find.EBaySite;
                    gridControl = keyword2Find.GridControl;
                    dataTable = (DataTable)gridControl.DataSource;
                    availableTo = keyword2Find.AvailableTo;
                    zip = keyword2Find.Zip;
                }
                else
                {

                }

                var itemID = foundItem.ItemID.Contains("|") ? foundItem.ItemID.Split('|')[0] : foundItem.ItemID;

                if (!string.IsNullOrEmpty(foundItem.Seller))
                {
                    if (SellerHelper.IsSellerBlocked(foundItem.Seller))
                    {
                        return new Tuple<string, DataRow?>("Seller in block list", null);
                    }
                }

                if (keyword2Find != null && foundItem.IsInitialSearch)
                {
                    if (keyword2Find.InitialSearchCount > keyword2Find.InitialSearchLimit)
                    {
                        return new Tuple<string, DataRow?>("Initial search limit reached", null);
                    }

                    var initialSearchSum = _ebaySearches.ChildrenCore.Sum(s => s.InitialSearchCount);
                    if (initialSearchSum > UserSettings.InitialResultsLimit)
                    {
                        return new Tuple<string, DataRow?>("Initial search limit reached", null);
                    }

                    keyword2Find.InitialSearchCount++;
                }
                else
                    UpdateItemSourcesStats(foundItem.ItemSource);

                if (CancelGetItemQueueIfSearchStopped(foundItem.ItemSource))
                    return new Tuple<string, DataRow?>("Search stopped", null);


                var existingRow = dataTable.Rows.Find(itemID);
                if (existingRow != null)
                {
                    if (foundItem.ItemSource == SearchSource.OOS)
                    {
                        dataTable.Rows.Remove(existingRow);
                    }
                    else
                    {
                        return new Tuple<string, DataRow?>("dataTable.Rows.Find(foundItem.ItemID) != null", null);
                    }
                }

                if (CacheManager.TryGetValue(itemID, out var datalist))
                {
                    row = dataTable.NewRow();
                    row["Blob"] = datalist;
                    DataListParser.CopyDataListToRow(row, datalist, gridControl);
                    if (Debugger.IsAttached)
                        row["Status Time"] = new DateTimeWithDiff(DateTime.UtcNow, null).LocalTime;
                    row["Sub Search"] = datalist.SubSearch;
                    row["Term"] = termAlias;
                    DateParser.SetFoundTime(datalist, datalist.StartTimeLocal?.UtcDateTime);
                    row["Found Time"] = datalist.FoundTime.LocalTime;
                }
                else
                {
                    //Parsing
                    datalist = DataListParser.InitDataList(foundItem, termAlias, siteCodeType, availableTo, zip);

                    if (foundItem.SimpleItem == null
                        || ProgramState.ColumnsUsedInFilters.Contains("Authenticity")
                        || ColumnsManager.ColumnVisible("Authenticity")
                        )
                    {
                        if (RequestQueueManager.Instance.GeneralItemQueueCount > 300 && foundItem.ItemSource != SearchSource.WAT)
                            return new Tuple<string, DataRow?>("Item queue limit reached :" + RequestQueueManager.Instance.GeneralItemQueueCount, null);

                        var itemGroup = new ItemGroup { Items = new[] { foundItem.BrowseItem } };
                        if (foundItem.BrowseItem == null)
                        {
                            itemGroup = await BrowseAPIParser.FetchBrowseAPIItem2Datalist(datalist, _searchService, datalist.Zip, datalist.AvailableTo, datalist.EBaySite.BrowseAPIID);
                            if (itemGroup == null)
                                return new Tuple<string, DataRow?>("BrowseAPI Item fetch error:" + RequestQueueManager.Instance.GeneralItemQueueCount, null);
                        }

                        var parsingError = BrowseAPIParser.ParseGroupItem2Datalist(datalist, itemGroup);
                        if (parsingError != null)
                        {
                            return parsingError;
                        }
                    }
                    else
                    {
                        if (SimpleItem2UbfItem(foundItem.SimpleItem, datalist))
                        {
                            return new Tuple<string, DataRow?>("SimpleItem convert fail", null);
                        }

                        datalist.SetStatus(RowStatusUpdater.SetItemStatus(foundItem.SimpleItem));
                        ItemParser.ParsePicturesShoppingAPI(datalist, foundItem.SimpleItem.PictureURL);
                        PricingServiceShoppingAPI.GetItemPricing(foundItem.SimpleItem, datalist.ItemPricing);
                    }

                    if (ConnectionConfig.TradingAPIEnabled)
                    {
                        // Helper method to check if a column is either used in filters or is visible.
                        bool IsMissingColumn(string columnName) => ProgramState.ColumnsUsedInFilters.Contains(columnName) || ColumnsManager.ColumnVisible(columnName);

                        // Determine if any of the conditions require parsing.
                        var isCommitToBuyInfoNeeded = EBayAccountsList.Any();
                        if (foundItem.BrowseItem?.ImmediatePay.HasValue == true)
                        {
                            if (foundItem.BrowseItem?.ImmediatePay.Value != true)
                            {
                                isCommitToBuyInfoNeeded = false;
                                datalist.CommitToBuy = true;
                                datalist.AutoPay = false;
                            }
                        }

                        var columnsMissingInBrowseApi = IsMissingColumn("Dispatch Days") || IsMissingColumn("Best Offer Count");

                        var pidsMissingInShoppingApi = foundItem.SimpleItem != null &&
                                                         (IsMissingColumn("ISBN") ||
                                                          IsMissingColumn("Brand") ||
                                                          IsMissingColumn("Model") ||
                                                          IsMissingColumn("MPN") ||
                                                          IsMissingColumn("UPC"));

                        var specificsMissingInShoppingApi = foundItem.SimpleItem != null && ItemSpecifics.CategorySpecificsList.Any(s => IsMissingColumn(s.CategoryName));

                        // Check if this is a UK/GB private seller that needs additional Trading API call for accurate pricing
                        var isUkGbPrivateSeller = SellerHelper.IsUkGbPrivateSeller(datalist);

                        // If shipping type is not set or any of the conditions is met, parse the item type.

                        if (string.IsNullOrEmpty(datalist.ShippingType) ||
                            isCommitToBuyInfoNeeded ||
                            columnsMissingInBrowseApi ||
                            pidsMissingInShoppingApi ||
                            specificsMissingInShoppingApi ||
                            isUkGbPrivateSeller)
                        {
                            var isBooks = false;
                            try
                            {
                                if (foundItem.CategoryPath != null)
                                    isBooks = Array.IndexOf(foundItem.CategoryPath, "267") > -1;
                            }
                            catch (Exception e)
                            {
                                Console.WriteLine(e);
                            }

                            if (!isBooks)
                            {
                                var result = await ParseItemType(datalist, foundItem.ItemSource, siteCodeType.SiteCode, availableTo, zip);
                                if (!string.IsNullOrEmpty(result))
                                {
                                    return Tuple.Create<string, DataRow?>(result, null);
                                }
                            }
                        }
                    }

                    DateParser.SetFoundTime(datalist, datalist.StartTimeLocal?.UtcDateTime);
                    //Seller validation
                    var sellerValidation = SellerHelper.ValidateSeller(datalist.SellerName, keyword2Find);
                    if (!sellerValidation.IsValid)
                    {
                        return new Tuple<string, DataRow?>(sellerValidation.ErrorMessage, null);
                    }

                    if (foundItem.ItemSource != SearchSource.WAT)
                        if (!_searchService.Running)
                        {
                            return new Tuple<string, DataRow?>("Search stopped", null);
                        }

                    string? sellerJsonStr;
                    if (string.IsNullOrEmpty(foundItem.SellerInfoJson))
                    {
                        sellerJsonStr = await _sellerService.GetSellerJsonStr(datalist.SellerName);
                    }
                    else
                    {
                        sellerJsonStr = foundItem.SellerInfoJson;
                        _sellerCacheService?.Set(datalist.SellerName, sellerJsonStr);
                    }

                    if (!string.IsNullOrEmpty(sellerJsonStr))
                    {
                        var sellerUser = _sellerService.ParseSellerInfo(sellerJsonStr);
                        if (sellerUser != null && !string.IsNullOrEmpty(sellerUser.UserName))
                        {
                            SellerHelper.PopulateSellerInfo(datalist, sellerUser, _countryService);
                        }
                    }

                    if (datalist.ShipToLocations != null && datalist.ExcludeShipToLocation != null)
                    {
                        datalist.ShipToLocations = CountryFilter.ReplaceRegionsWithCountries(datalist.ShipToLocations, datalist.ExcludeShipToLocation);
                        datalist.ExcludeShipToLocation = CountryFilter.ConvertExcludedRegionsToCountries(datalist.ExcludeShipToLocation.ToArray()).ToList();


                        if (!ItemParser.IsCountryMatch(datalist, availableTo))
                        {
                            return new Tuple<string, DataRow?>("Country doesn't match", row);
                        }
                    }

                    ItemParser.ParseSimpleFields(datalist);

                    datalist.Shipping = datalist.ItemShipping.FullSingleShippingPrice.Value;
                    datalist.ItemPrice = datalist.ItemPricing.ItemPrice.Value;
                    datalist.TotalPrice = datalist.ItemPricing.GetTotalPrice(datalist.ItemShipping.FullSingleShippingPrice).Value;

                    if (datalist.ItemShipping.ShipAdditionalItem.Value >= 0)
                    {
                        datalist.ShipAdditionalItem = datalist.ItemShipping.ShipAdditionalItem.Value;
                    }

                    if (datalist.ItemPricing.AuctionPrice != null)
                    {
                        datalist.AuctionPrice = datalist.ItemPricing.AuctionPrice.Value;
                    }

                    if (keyword2Find != null && UniversalItemParser.IsPriceZero(datalist, keyword2Find))
                    {
                        return new Tuple<string, DataRow?>("Price is 0", null);
                    }

                    if (string.IsNullOrEmpty(datalist.Brand))
                    {

                    }
                    row = dataTable.NewRow();
                    row["Blob"] = datalist;
                    if (keyword2Find != null)
                    {
                        var itemTitleMatches =
                            KeywordHelpers.IsMatch(datalist.Title, keyword2Find.LuceneQuery);
                        if (itemTitleMatches)
                            Stat.TitleMatchCounter++;
                        else
                            Stat.TitleNotMatchCounter++;

                        row["Title Match"] = itemTitleMatches || string.IsNullOrWhiteSpace(keyword2Find.Kws);
                    }

                    DataListParser.CopyDataListToRow(row, datalist, gridControl);
                    ItemParser.PurgeDatalistDescription(datalist);
                }

                var subSearchesExist = keyword2Find != null && keyword2Find.ChildrenCore.Any(a => a.Enabled);
                var isSubSearchMatch = false;
                if (subSearchesExist)
                    isSubSearchMatch = await SubSearch.SubSearch.IsSubSearchMatch(keyword2Find, row, datalist);

                // Two-pass filtering system to save AI API calls
                var removeFilterTriggered = false;
                if (foundItem.ItemSource != SearchSource.WAT)
                    if (!subSearchesExist || isSubSearchMatch)
                    {
                        // First pass: Apply non-AI filters only
                        var nonAiFilterResult = XFilterManager.ApplyNonAiRemoveRowFilter(row);
                        if (!string.IsNullOrEmpty(nonAiFilterResult))
                        {
                            removeFilterTriggered = true;
                            Debug.WriteLine($"Item {itemID} removed by non-AI filter: {nonAiFilterResult}");
                        }
                        else
                        {
                            // Second pass: Only if item passed non-AI filters, perform AI analysis and AI filtering
                            if (UserSettings.ExternalDataEnabled)
                            {
                                try
                                {
                                    await AiAnalysis.PerformSingleRowAnalysis(row);
                                    Debug.WriteLine($"AI analysis completed for item {itemID}");

                                    // Apply AI filters after AI analysis
                                    var aiFilterResult = XFilterManager.ApplyAiRemoveRowFilter(row);
                                    if (!string.IsNullOrEmpty(aiFilterResult))
                                    {
                                        removeFilterTriggered = true;
                                        Debug.WriteLine($"Item {itemID} removed by AI filter: {aiFilterResult}");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Debug.WriteLine($"AI analysis failed for item {itemID}: {ex.Message}");
                                    // Continue processing even if AI analysis fails
                                }
                            }
                            else if (UserSettings.ExternalDataEnabled && row != null && !XFilterManager.HasEnabledAiFilters())
                            {
                                Debug.WriteLine($"Skipping AI analysis for item {itemID} - no AI filters enabled");
                            }
                        }
                    }

                if (dataTable.Rows.Contains(row))
                {
                    return new Tuple<string, DataRow?>("Duplicate row", row);
                }

                if (ItemParser.IsRowCellNull(row))
                {
                    return new Tuple<string, DataRow?>("ItemID null", row);
                }

                if (datalist.ItemStatus == ItemStatus.OtherListingError
                    || datalist.ItemStatus == ItemStatus.NotAvailable
                    || datalist.ItemStatus == ItemStatus.Incorrect
                    || datalist.ItemStatus == ItemStatus.LostOrBroken
                    || datalist.ItemStatus == ItemStatus.SellToHighBidder)
                {
                    return new Tuple<string, DataRow?>("Listing status not Active", row);
                }

                if (subSearchesExist)
                    if (!isSubSearchMatch)
                    {
                        datalist.Description = row["Description"].ToString();
                        datalist.Row = null;
                        CacheManager.AddItem(datalist);

                        return new Tuple<string, DataRow?>("Cached", row);
                    }

                if (removeFilterTriggered)
                {
                    return new Tuple<string, DataRow?>("Filter doesn't match", row);
                }

                ItemParser.AssignAccountToBuyWIth(row, datalist);
                if (foundItem.SimpleItem == null)
                    ItemParser.SetNoDescriptionWarning(row);

                if (ItemParser.IsTitleEmpty(datalist))
                {
                    return new Tuple<string, DataRow?>("Title empty", row);
                }

                var grView = (AdvBandedGridView)gridControl.MainView;
                if (grView == null)
                {
                    return new Tuple<string, DataRow?>("Gridview null", row);
                }

                if (dataTable.Rows.Find(itemID) != null)
                {
                    return new Tuple<string, DataRow?>("dataTable.Rows.Find(foundItem.ItemID) is already present", row);
                }
                if (barCheckItemTotalPriceAsMaxPrice.Checked)
                    if (keyword2Find != null && datalist.TotalPrice > keyword2Find.PriceMax && datalist.QuantityAvailable == 1)
                    {
                        return new Tuple<string, DataRow?>("Total Price > PriceMax", row);
                    }

                grView.BeginUpdate();
                dataTable.Rows.Add(row);
                if (foundItem.ItemSource != SearchSource.WAT)
                    TrimRowOverLimit(dataTable, UserSettings.MaxResultsCount);
                grView.EndUpdate();

                var thumbnailColumn = grView.Columns.ColumnByFieldName("Thumbnail");
                if (thumbnailColumn == null)
                {
                    return new Tuple<string, DataRow?>("thumbnailColumn null", row);
                }

                if (SearchConfigManager.Instance.DownloadAvatars && thumbnailColumn.Visible)
                {
                    ImageTools.QueueImageToRow(datalist.GalleryUrl, row);
                }

                if (datalist.ItemStatus == ItemStatus.Active)
                    if (datalist.Source != SearchSource.WAT)
                        if (datalist.Source == SearchSource.RSS3 || !foundItem.IsInitialSearch)
                        {
                            if (ProgramState.TotalRunningStopwatch.Elapsed.TotalMinutes > 3)
                                CreateNotifications(row, datalist, gridControl);
                        }

                // Process Restock filters for active items (non-blocking)
                if (datalist.ItemStatus == ItemStatus.Active && UserSettings.RestockerEnabled)
                {
                    // Fire and forget - don't wait for restock processing to complete
                    // This allows the item to be added to the grid immediately
                    _ = ProcessRestockFiltersInBackgroundAsync(row, itemID);
                }

                ManageRowFocus(grView, dataTable);

                if (!foundItem.IsInitialSearch
                    && ProgramState.InitialSearchCompleted
                    && datalist.ItemStatus == ItemStatus.Active
                    && barCheckItemMaximizewindow.Checked
                    && !WindowFocus.ApplicationIsActivated())
                {
                    Activate();
                }

                if (datalist.ItemStatus == ItemStatus.Active)
                {
                    _trayManager.PopupFromTray(null);
                }
            }
            catch (ConstraintException)
            {
                if (ProgramState.Isdebug)
                {
                    //	MessageBox.Show(ex.Message,@"Duplicate: ");
                }

                //gridView1.EndDataUpdate();
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("case 9: ", ex);
            }

            try
            {
                switch (row?.RowState)
                {
                    case DataRowState.Added:
                    case DataRowState.Modified:
                    case DataRowState.Unchanged:
                        break;
                    case DataRowState.Detached:
                    case DataRowState.Deleted:
                        row.CancelEdit();

                        break;
                }
            }
            catch (Exception)
            {
                // ignored
            }

            return new Tuple<string, DataRow?>("", row);
        }


        private async Task<string> ParseItemType(DataList datalist, SearchSource searchSource, SiteCodeType siteCodeType, string availableTo, string keyword2FindZip)
        {
            var theItem = await AddDataFromGetItem(datalist.ItemID, siteCodeType, datalist.Source);

            if (searchSource != SearchSource.WAT && theItem != null && ItemParser.IsOutOfStock(theItem.ReasonHideFromSearch))
            {
                return "Item is out of stock";
            }

            if (ItemType2UbfItem(theItem, datalist))
            {
                return "ItemType convert fail";
            }

            datalist.ItemSpecifics = theItem.ItemSpecifics;
            datalist.SetStatus(RowStatusUpdater.SetItemStatus(theItem));
            if (theItem.PictureDetails != null)
            {
                ItemParser.ParsePictures(datalist, theItem.PictureDetails);
            }

            var apiService = new ApiService(ConnectionConfig.GetGuestApiContext(siteCodeType));
            var pricingService = new PricingService(apiService);

            var isInternational = availableTo != datalist.FromCountry;
            Enum.TryParse(availableTo, true, out CountryCodeType destinationCountry);

            // Check if this is a UK/GB private seller that needs special price handling
            var isUkGbPrivateSeller = SellerHelper.IsUkGbPrivateSeller(datalist);

            if (isUkGbPrivateSeller)
            {
                // For UK/GB private sellers, store the original Browse API price (with fees) for display
                var originalDisplayPrice = datalist.ItemPricing.ItemPrice;

                // Get the Trading API price (without fees) for purchase
                PricingService.GetBuyItNowAndAuctionPrice(theItem, datalist);
                var purchasePrice = datalist.ItemPricing.ItemPrice;

                // Set both prices: display price (with fees) and purchase price (without fees)
                datalist.ItemPricing.ItemPrice = originalDisplayPrice;  // Keep original for display
                datalist.ItemPricing.PurchasePrice = purchasePrice;     // Store Trading API price for purchase
            }
            else
            {
                // For other sellers, use normal pricing logic
                PricingService.GetBuyItNowAndAuctionPrice(theItem, datalist);
            }

            await pricingService.GetItemPricing(theItem, datalist, isInternational, destinationCountry, keyword2FindZip);

            return "";
        }

        private bool CancelGetItemQueueIfSearchStopped(SearchSource searchSource)
        {
            if (searchSource != SearchSource.WAT)
                if (!_searchService.Running)
                {
                    // Use SearchConfigManager.ResetQueueLimiters to properly release all queue limiters
                    SearchConfigManager.Instance.ResetQueueLimiters();
                    return true;
                }

            return false;
        }



        private static bool ItemType2UbfItem(ItemType theItem, DataList dataList)
        {
            if (theItem == null)
            {
                return true;
            }

            if (theItem.RelistParentID != 0)
                dataList.RelistParentID = theItem.RelistParentID.ToString();
            dataList.AutoPay = theItem.AutoPay; //every item, can't remove while placeoffer enabled, can skip if ebay account not added.
            dataList.CommitToBuy = !theItem.AutoPay; //every item, can't remove while placeoffer enabled, can skip if ebay account not added.

            dataList.DispatchDays = theItem.DispatchTimeMax; //every item, able to remove
            dataList.BestOfferCount = theItem.BestOfferDetails?.BestOfferCount ?? 0; //every item, able to remove

            dataList.SetShippingType(theItem.ShippingDetails.ShippingType.ToString()); // able to remove OR request as a backup
            dataList.QuantityTotal = theItem.Quantity; //able to remove OR request as a backup
            dataList.QuantityAvailable = theItem.Quantity - theItem.SellingStatus.QuantitySold; //able to remove OR request as a backup

            if (theItem.PickupInStoreDetails is { EligibleForPickupInStore: true })
                dataList.ShippingDelivery = string.Join(",", dataList.ShippingDelivery, "In Store Pickup").Trim(',');
            var shippingServiceOptions = theItem.ShippingDetails.ShippingServiceOptions.ToArray();
            if (theItem.ShippingDetails is { ShippingServiceOptions: not null })
            {
                if (!dataList.ShippingDelivery.Contains("Ship To Home") && shippingServiceOptions.Any(s => !s.LocalPickup))
                    dataList.ShippingDelivery = string.Join(",", dataList.ShippingDelivery, "Ship To Home").Trim(',');

                if (shippingServiceOptions.Any(s => s.LocalPickup))
                    dataList.ShippingDelivery = string.Join(",", dataList.ShippingDelivery, "Local Pickup").Trim(',');
            }

            dataList.EndTime = new DateTimeOffset(theItem.ListingDetails.EndTime, TimeSpan.Zero);
            dataList.Description = theItem.Description;

            //If FoundItem from another api
            if (dataList.SourceB == DataList.Source2.SS)
            {
                var payments = theItem.PaymentMethods.ToArray().ToList().Select(p => p.ToString()).ToArray();
                dataList.Payment = string.Join(",", payments);
                dataList.StoreName = SellerHelper.GetStoreName(theItem);
                if (theItem.VATDetails != null)
                    dataList.VATNumber = $"{theItem.VATDetails.VATSite + theItem.VATDetails.VATID}".Trim();

                var pidList = new List<string>();
                if (theItem.ProductListingDetails?.UPC != null)
                    dataList.UPC = theItem.ProductListingDetails.UPC ?? "";
                if (theItem.ProductListingDetails?.BrandMPN?.Brand != null)
                    dataList.Brand = theItem.ProductListingDetails.BrandMPN.Brand ?? "";
                if (theItem.ProductListingDetails?.BrandMPN?.Brand != null)
                    dataList.MPN = theItem.ProductListingDetails.BrandMPN.MPN ?? "";
                if (theItem.ProductListingDetails?.ProductReferenceID != null)
                    dataList.ProductReferenceID = theItem.ProductListingDetails.ProductReferenceID ?? "";

                //dataList.Model=
                var modelSpecific = (theItem.ItemSpecifics?.ToArray() ?? Array.Empty<NameValueListType>()).FirstOrDefault(s => s.Name == "Model");
                var model = string.Join(",", modelSpecific?.Value.ToArray() ?? Array.Empty<string>());
                dataList.Model = model;
                if (theItem.ProductListingDetails?.ISBN != null)
                    pidList.Add(theItem.ProductListingDetails.ISBN);
                if (theItem.ProductListingDetails?.EAN != null)
                    pidList.Add(theItem.ProductListingDetails.EAN);

                pidList.AddRange(dataList.UPC.Split(',').ToList());
                dataList.UPC = string.Join(",", pidList);
                Helpers.CleanPIDs(dataList);
            }

            if (ConnectionConfig.BrowseAPIEnabled)
                return false;
            DateParser.SetEndTime_Timeleft(dataList, (DateTimeOffset)dataList.EndTime);
            dataList.ItemID = theItem.ItemID;
            dataList.SellerRegistration = new DateTimeOffset(theItem.Seller.RegistrationDate);
            dataList.ShipToLocations = theItem.ShipToLocations.ToArray().ToList();
            dataList.ExcludeShipToLocation = theItem.ShippingDetails.ExcludeShipToLocation.ToArray().ToList();

            dataList.BuyItNowAvailable = theItem.ListingDetails.BuyItNowAvailable;

            if (theItem.ListingType != ListingTypeCodeType.Auction && theItem.ListingType != ListingTypeCodeType.Chinese)
                dataList.ListingCodeType = new[] { "Buy It Now" };
            else if (dataList.BuyItNowAvailable)
            {
                dataList.ListingCodeType = new[] { "Buy It Now", "Auction" };
            }
            else
            {
                dataList.ListingCodeType = new[] { "Auction" };
            }

            SellerHelper.PopulateSellerInfoFromItem(dataList, theItem);
            dataList.FromCountry = theItem.Country.ToString();
            dataList.BestOffer = theItem.BestOfferDetails?.BestOfferEnabled ?? false;
            bool dataListVariation;
            if (theItem.Variations?.Variation?.Count != null)
                dataListVariation = theItem.Variations.Variation.Count > 1;
            else
                dataListVariation = false;

            dataList.Variation = dataListVariation;
            dataList.Location = theItem.Location;

            dataList.Title = theItem.Title;

            dataList.Bids = theItem.SellingStatus.BidCount;

            dataList.CategoryName = theItem.PrimaryCategory?.CategoryName ?? "";
            dataList.CategoryID = theItem.PrimaryCategory?.CategoryID ?? "";
            if (!string.IsNullOrEmpty(theItem.SecondaryCategory?.CategoryID))
                dataList.CategoryID += ", " + theItem.SecondaryCategory?.CategoryID;
            dataList.Condition = theItem.ConditionDisplayName;
            dataList.ConditionID = theItem.ConditionID.ToString();

            dataList.Site = theItem.Site;

            dataList.PageViews = theItem.HitCount;
            dataList.ConditionDescription = theItem.ConditionDescription;

            var returnsAccepted = theItem.ReturnPolicy?.ReturnsAccepted ?? "";
            var returnsWithin = theItem.ReturnPolicy?.ReturnsWithin ?? "";
            dataList.Returns = returnsAccepted;
            if (!string.IsNullOrEmpty(returnsAccepted))
                dataList.Returns += " / " + returnsWithin;

            var shippingTimeMax = shippingServiceOptions.ToList().FirstOrDefault()?.ShippingTimeMax ?? 0;
            dataList.ShippingDays = shippingTimeMax;

            return false;
        }

        private static bool SimpleItem2UbfItem(ShoppingAPIJson.SimpleItemType? theItem, DataList dataList)
        {
            try
            {
                if (theItem == null)
                {
                    return true;
                }

                //if (ItemParser.IsOutOfStock(theItem.ReasonHideFromSearch))
                //{
                //   return true;
                //}

                dataList.AutoPay = theItem.AutoPay;
                if (ConnectionConfig.TradingAPIEnabled)
                    dataList.CommitToBuy = !theItem.AutoPay;
                dataList.ItemID = theItem.ItemID;
                dataList.EndTime = new DateTimeOffset(theItem.EndTime, TimeSpan.Zero);
                DateParser.SetEndTime_Timeleft(dataList, (DateTimeOffset)dataList.EndTime);
                DateParser.SetPostedTime(dataList, theItem.StartTime);

                //it.BestOfferCount = theItem.BestOfferDetails?.BestOfferCount;//api missing
                if (theItem.ShipToLocations == null)
                    dataList.ShipToLocations = new List<string>();
                else
                    dataList.ShipToLocations = theItem.ShipToLocations.ToArray().ToList();
                if (theItem.ExcludeShipToLocation == null)
                    dataList.ExcludeShipToLocation = new List<string>();
                else
                    dataList.ExcludeShipToLocation = theItem.ExcludeShipToLocation.ToArray().ToList();
                //it.ShippingType = theItem.ShippingDetails.ShippingType.ToString();

                dataList.SetShippingType("");

                dataList.QuantityTotal = theItem.Quantity;
                dataList.QuantityAvailable = theItem.Quantity - theItem.QuantitySold;

                dataList.BuyItNowAvailable = theItem.BuyItNowAvailable;

                if (theItem.ListingType != "Auction " && theItem.ListingType != "Chinese")
                    dataList.ListingCodeType = new[] { "Buy It Now" };
                else if (dataList.BuyItNowAvailable)
                {
                    dataList.ListingCodeType = new[] { "Buy It Now, Auction" };
                }
                else
                {
                    dataList.ListingCodeType = new[] { "Auction" };
                }

                //it.RelistParentID = theItem.RelistParentID.ToString();//api missing

                SellerHelper.PopulateSellerInfoFromSimpleItem(dataList, theItem);
                dataList.FromCountry = theItem.Country;

                dataList.BestOffer = theItem.BestOfferEnabled;
                //it.VariationCount = theItem.Variations?.Variation?.Count ?? 0;
                dataList.Location = theItem.Location;

                dataList.Title = theItem.Title;

                dataList.Bids = theItem.BidCount;
                dataList.CategoryName = theItem.PrimaryCategoryName.Replace(":", "|");
                dataList.CategoryID = theItem.PrimaryCategoryID;
                if (!string.IsNullOrEmpty(theItem.SecondaryCategoryID))
                    dataList.CategoryID += ", " + theItem.SecondaryCategoryID;

                dataList.Condition = theItem.ConditionDisplayName;
                dataList.ConditionID = theItem.ConditionID.ToString();

                dataList.Site = (SiteCodeType)Enum.Parse(typeof(SiteCodeType), theItem.Site);

                dataList.PageViews = theItem.HitCount;
                dataList.ConditionDescription = theItem.ConditionDescription;
                var paymentMethods = theItem.PaymentMethods.ToArray().ToList().Select(p => p.ToString()).ToArray();
                dataList.Payment = string.Join(",", paymentMethods);

                var returnsAccepted = theItem.ReturnPolicy?.ReturnsAccepted ?? "";
                var returnsWithin = theItem.ReturnPolicy?.ReturnsWithin ?? "";
                dataList.Returns = returnsAccepted;
                if (!string.IsNullOrEmpty(returnsAccepted))
                    dataList.Returns += " / " + returnsWithin;

                var pidList = new List<string>();
                if (!string.IsNullOrEmpty(theItem.ProductID?.Value))
                {
                    var pidType = theItem.ProductID?.Type ?? "";
                    switch (pidType)
                    {
                        case "Reference":
                            dataList.ProductReferenceID = theItem.ProductID?.Value ?? "";
                            break;
                        case "MPN":
                            pidList.Add(theItem.ProductID?.Value ?? "");
                            break;
                        case "ISBN":
                            pidList.Add(theItem.ProductID?.Value ?? "");
                            break;
                        case "EAN":
                            pidList.Add(theItem.ProductID?.Value ?? "");
                            break;
                        case "UPC":
                            pidList.Add(theItem.ProductID?.Value ?? "");
                            break;
                    }
                }
                if (!string.IsNullOrEmpty(theItem.SKU))
                    pidList.Add(theItem.SKU);

                dataList.UPC = string.Join(",", pidList);
                Helpers.CleanPIDs(dataList);

                dataList.Description = theItem.Description;
                //it.Brand = theItem.ProductListingDetails?.BrandMPN?.Brand;
                //it.MPN = theItem.ProductListingDetails?.BrandMPN?.MPN;
                //it.ISBN = theItem.ProductListingDetails?.ISBN;
                //it.EAN = theItem.ProductListingDetails?.EAN;
                //it.ProductReferenceID = theItem.ProductListingDetails?.ProductReferenceID;

                //var shippingTimeMax = theItem.ShippingDetails.ShippingServiceOptions.ToArray().ToList().FirstOrDefault()?.ShippingTimeMax ?? 0;
                //it.DispatchTimeMax = theItem.DispatchTimeMax + shippingTimeMax;

                return false;
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);

                return true;
            }
        }

        private void CreateNotifications(DataRow row, DataList datalist, GridControl gridControl)
        {
            SendTelegram(row);

            SendPushbullet(row);

            ShowAlert(row, datalist, gridControl).ConfigureAwait(false);

            if (barCheckItemSoundAlert.Checked)
            {
                _myPlayer.Play();
            }

            if (UserSettings.OpenInBrowser == UserSettings.OpenInBrowserEnum.ItemPage)
            {
                Browser.OpenAffiliateLink(datalist);
            }

            if (UserSettings.OpenInBrowser == UserSettings.OpenInBrowserEnum.CheckoutPage)
            {
                Browser.OpenCheckoutLink(datalist);
            }

            if (SearchConfigManager.Instance.DownloadOtherImages && dockPictures.Visibility == DockVisibility.Visible)
            {
                foreach (var url in datalist.Pictures)
                {
                    ImageTools.QueueImageToDisk(url);
                }
            }
        }

        private void ManageRowFocus(AdvBandedGridView grView, DataTable dataTable)
        {
            if ((ProgramState.InitialSearchCompleted && ProgramState.Idlesw.Elapsed.TotalSeconds > TopRowFocus.TopRowFocusInterval
                 || grView.RowCount < 5
                 || ProgramState.InitialSearchCompleted /*&& !WindowFocus.ApplicationIsActivated()*/)
                && dataTable.Rows.Count > 0
                || ProgramState.InitialSearchCompleted && TopRowFocus.TopRowFocusInterval < 2)
            {
                if (TopRowFocus.TopRowFocusInterval < 2)
                {
                    TopRowFocus.GridView = grView;
                    TopRowFocus.RowHandle = grView.GetRowHandle(dataTable.Rows.Count - 1);
                    if (TopRowFocus.TopRowFocusInterval == 0)
                    {
                        TopRowFocus.FocusTopRow();
                    }
                    else
                    {
                        TopRowFocus.FocusTimer.Interval = TopRowFocus.TopRowFocusInterval * 1000;
                        QueueTopRowFocus(grView, grView.GetRowHandle(dataTable.Rows.Count - 1));
                    }
                }
                else
                {
                    TopRowFocus.FocusTimer.Interval = 50;
                    QueueTopRowFocus(grView, grView.GetRowHandle(dataTable.Rows.Count - 1));
                }
            }
        }

        private async Task ShowAlert(DataRow row, DataList datalist, GridControl gridControl)
        {
            if (AlertOptionsClass.AlertOptions.Enabled)
            {
                var title = AlertOptionsClass.AlertOptions.TitleTemplate;
                var body = AlertOptionsClass.AlertOptions.BodyTemplate;
                foreach (var columnName in AlertOptionsClass.AlertOptions.TitleColumns)
                {
                    title = title.Replace("{" + columnName + "}", AlertOptionsClass.GetRowValue(row, columnName, datalist));
                }

                foreach (var columnName in AlertOptionsClass.AlertOptions.BodyColumns)
                {
                    body = body.Replace("{" + columnName + "}", AlertOptionsClass.GetRowValue(row, columnName, datalist));
                }

                var alertInfo = new AlertInfo(title, body) { Tag = new Tuple<DataRow, GridControl>(row, gridControl) };

                if (SearchConfigManager.Instance.DownloadAvatars && AlertOptionsClass.AlertOptions.ShowImage)
                {
                    var avatarImagePath = ImageTools.QueueImageToRow(datalist.GalleryUrl, row);
                    if (File.Exists(avatarImagePath))
                    {
                        var imageBitmap = await ImageTools.ReadFileToBitmap(avatarImagePath);
                        var maxWidth = AlertOptionsClass.AlertOptions.Width / 3;
                        if (imageBitmap.Size.Width > maxWidth)
                        {
                            imageBitmap = ImageProcessor.ResizeImage(imageBitmap, maxWidth, imageBitmap.Size.Height, true, false);
                        }

                        alertInfo.Image = imageBitmap; //image access exceptions
                    }
                }

                alertControl1.Show(this, alertInfo);
            }
        }

        private void SendPushbullet(DataRow row)
        {
            if (_pushbullet?.Enabled == true)
            {
                if (_pushbullet.CombinePushes == 1)
                {
                    _pushbullet?.PushLink(row);
                }
                else
                {
                    _pushbullet?.PushLinksNote(row);
                }
            }
        }

        private void SendTelegram(DataRow row)
        {
            if (_telegramSender?.Enabled == true)
            {
                var result = XFilterManager.IsMatchForTelegram(row);
                if (!string.IsNullOrWhiteSpace(result))
                    _telegramSender?.PushItem(row);
            }
        }

        private static void TrimRowOverLimit(DataTable dataTable, int maxRowNumber)
        {
            while (dataTable.Rows.Count > maxRowNumber)
            {
                var i = 0;
                while (i < 10 && dataTable.Rows.Count > 0)
                {
                    dataTable.Rows.RemoveAt(0);
                    i++;
                }
            }
        }

        private static void UpdateItemSourcesStats(SearchSource realSource)
        {
            if (!ProgramState.InitialSearchCompleted || !(ProgramState.TotalRunningStopwatch.Elapsed.TotalMinutes > 15))
            {
                return;
            }

            switch (realSource)
            {
                case SearchSource.API:
                    Stat.ApiItem_Source++;
                    break;

                case SearchSource.API2:
                    Stat.BrowseApiItem_Source++;
                    break;

                case SearchSource.S4:
                    Stat.S4Item_Source++;
                    break;

                case SearchSource.RSS3:
                    Stat.Rss3Item_Source++;
                    break;
            }
        }

        private void QueueTopRowFocus(AdvBandedGridView grView, int rowHandle)
        {
            TopRowFocus.GridView = grView;
            TopRowFocus.RowHandle = rowHandle;
            TopRowFocus.FocusTimer.Enabled = true;
        }

        private void FocusTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            TopRowFocus.FocusTopRow();
        }

        private async Task<ItemType?> AddDataFromGetItem(string itemID, SiteCodeType siteCodeType, SearchSource source)
        {
            try
            {
                return await GetSpecificsAsyncRss(itemID, ConnectionConfig.GetGuestApiContext(siteCodeType), source);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("GetSpecificsAsync: ", ex);
            }

            return null;
        }

        /// <summary>
        /// Processes restock filters in the background without blocking UI
        /// </summary>
        private async Task ProcessRestockFiltersInBackgroundAsync(DataRow row, string itemID)
        {
            try
            {
                // Use ConfigureAwait(false) to avoid capturing synchronization context
                // This prevents potential deadlocks and cross-thread issues
                var restockResult = await XFilterManager.ProcessRestockFiltersAsync(row).ConfigureAwait(false);

                if (!string.IsNullOrEmpty(restockResult))
                {
                    Debug.WriteLine($"Item {itemID} matched Restock filters: {restockResult}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error processing Restock filters for item {itemID}: {ex.Message}");
                // Continue processing even if Restock filters fail
            }
        }
    }
}
