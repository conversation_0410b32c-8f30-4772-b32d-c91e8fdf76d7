﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service for exporting reports to various formats
    /// </summary>
    public class ReportExportService : IReportExportService
    {
        private readonly string _htmlStorageDirectory;

        public ReportExportService()
        {
            // Default HTML storage directory
            _htmlStorageDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "Restocker", "HtmlSteps");

            // Ensure directory exists
            Directory.CreateDirectory(_htmlStorageDirectory);
        }

        public ReportExportService(string htmlStorageDirectory)
        {
            _htmlStorageDirectory = htmlStorageDirectory ?? throw new ArgumentNullException(nameof(htmlStorageDirectory));
            Directory.CreateDirectory(_htmlStorageDirectory);
        }

        /// <summary>
        /// Exports user reports to CSV format
        /// </summary>
        public async Task ExportUserReportToCsvAsync(IEnumerable<UserReport> reports, string filePath)
        {
            if (reports == null)
                throw new ArgumentNullException(nameof(reports));

            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be empty", nameof(filePath));

            var csv = new StringBuilder();

            // Add header
            csv.AppendLine("Time,KeywordId,JobId,KeywordAlias,Keywords,RequiredQuantity,PurchasedQuantity," +
                          "LastOrderStatus,PriceMin,PriceMax,Categories,Condition,EbaySite,LocatedIn,ShipsTo," +
                          "ShipZipcode,Sellers,SellerType,Interval,Threads,ViewName,ListingType,SearchInDescription," +
                          "RemainingQuantity,CompletionPercentage,IsCompleted,LastTransactionTime,ReportGeneratedAt");

            // Add data rows
            foreach (var report in reports)
            {
                csv.AppendLine(FormatUserReportForCsv(report));
            }

            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }

        /// <summary>
        /// Exports a transaction detail report to CSV format
        /// </summary>
        public async Task ExportTransactionDetailReportToCsvAsync(TransactionDetailReport report, string filePath)
        {
            if (report == null)
                throw new ArgumentNullException(nameof(report));

            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be empty", nameof(filePath));

            var csv = new StringBuilder();

            // Add report header information
            csv.AppendLine($"Transaction Detail Report");
            csv.AppendLine($"Keyword ID: {report.KeywordId}");
            csv.AppendLine($"Job ID: {report.JobId}");
            csv.AppendLine($"Keyword Alias: {report.KeywordAlias}");
            csv.AppendLine($"Generated At: {report.GeneratedAt:yyyy-MM-dd HH:mm:ss}");
            csv.AppendLine($"Total Quantity Purchased: {report.TotalQuantityPurchased}");
            csv.AppendLine($"Total Amount Spent: {report.TotalAmountSpent:C}");
            csv.AppendLine($"Successful Attempts: {report.SuccessfulAttempts}");
            csv.AppendLine($"Failed Attempts: {report.FailedAttempts}");
            csv.AppendLine();

            // Add transactions section
            if (report.Transactions.Any())
            {
                csv.AppendLine("TRANSACTIONS");
                csv.AppendLine("Id,ItemId,ItemTitle,Status,Quantity,PurchasePrice,PurchaseDate,TransactionId,PaymentMethod,Notes");

                foreach (var transaction in report.Transactions.OrderBy(t => t.PurchaseDate))
                {
                    csv.AppendLine(FormatTransactionForCsv(transaction));
                }
                csv.AppendLine();
            }

            // Add attempts section
            if (report.Attempts.Any())
            {
                csv.AppendLine("PURCHASE ATTEMPTS");
                csv.AppendLine("Id,ItemId,AttemptDate,Result,ErrorMessage");

                foreach (var attempt in report.Attempts.OrderBy(a => a.AttemptDate))
                {
                    csv.AppendLine(FormatAttemptForCsv(attempt));
                }
            }

            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }

        /// <summary>
        /// Saves HTML content from a purchase step to a file
        /// </summary>
        public async Task<string> SaveHtmlContentAsync(string htmlContent, string keywordId, string jobId, DateTime timestamp)
        {
            if (string.IsNullOrEmpty(htmlContent))
                throw new ArgumentException("HTML content cannot be empty", nameof(htmlContent));

            if (string.IsNullOrEmpty(keywordId))
                throw new ArgumentException("Keyword ID cannot be empty", nameof(keywordId));

            if (string.IsNullOrEmpty(jobId))
                throw new ArgumentException("Job ID cannot be empty", nameof(jobId));

            // Create filename with timestamp, keyword, and job ID
            var safeKeywordId = MakeFilenameSafe(keywordId);
            var safeJobId = MakeFilenameSafe(jobId);
            var timestampStr = timestamp.ToString("yyyyMMdd_HHmmss");
            var filename = $"{timestampStr}_{safeKeywordId}_{safeJobId}.html";

            var filePath = Path.Combine(_htmlStorageDirectory, filename);

            File.WriteAllText(filePath, htmlContent, Encoding.UTF8);

            return filePath;
        }

        /// <summary>
        /// Gets the directory path where HTML files are stored
        /// </summary>
        public string GetHtmlStorageDirectory()
        {
            return _htmlStorageDirectory;
        }

        /// <summary>
        /// Cleans up old HTML files based on retention policy
        /// </summary>
        public async Task<int> CleanupOldHtmlFilesAsync(int retentionDays = 30)
        {
            if (!Directory.Exists(_htmlStorageDirectory))
                return 0;

            var cutoffDate = DateTime.Now.AddDays(-retentionDays);
            var files = Directory.GetFiles(_htmlStorageDirectory, "*.html");
            var deletedCount = 0;

            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                if (fileInfo.CreationTime < cutoffDate)
                {
                    try
                    {
                        File.Delete(file);
                        deletedCount++;
                    }
                    catch
                    {
                        // Ignore errors when deleting files
                    }
                }
            }

            await Task.CompletedTask;
            return deletedCount;
        }

        private string FormatUserReportForCsv(UserReport report)
        {
            return string.Join(",", new[]
            {
                EscapeCsvField(report.LastTransactionTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""),
                EscapeCsvField(report.KeywordId),
                EscapeCsvField(report.JobId),
                EscapeCsvField(report.KeywordAlias),
                EscapeCsvField(report.Keywords),
                report.RequiredQuantity.ToString(),
                report.PurchasedQuantity.ToString(),
                EscapeCsvField(report.LastOrderStatus),
                report.PriceMin.ToString(CultureInfo.InvariantCulture),
                report.PriceMax.ToString(CultureInfo.InvariantCulture),
                EscapeCsvField(report.Categories),
                EscapeCsvField(report.Condition),
                EscapeCsvField(report.EbaySite),
                EscapeCsvField(report.LocatedIn),
                EscapeCsvField(report.ShipsTo),
                EscapeCsvField(report.ShipZipcode),
                EscapeCsvField(report.Sellers),
                EscapeCsvField(report.SellerType),
                EscapeCsvField(report.Interval.ToString()),
                report.Threads.ToString(),
                EscapeCsvField(report.ViewName),
                EscapeCsvField(report.ListingType),
                report.SearchInDescription.ToString(),
                report.RemainingQuantity.ToString(),
                report.CompletionPercentage.ToString("F2", CultureInfo.InvariantCulture),
                report.IsCompleted.ToString(),
                EscapeCsvField(report.LastTransactionTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""),
                EscapeCsvField(report.ReportGeneratedAt.ToString("yyyy-MM-dd HH:mm:ss"))
            });
        }

        private string FormatTransactionForCsv(PurchaseTransaction transaction)
        {
            return string.Join(",", new[]
            {
                transaction.Id.ToString(),
                EscapeCsvField(transaction.ItemId),
                EscapeCsvField(transaction.ItemTitle),
                EscapeCsvField(transaction.Status),
                transaction.Quantity.ToString(),
                transaction.PurchasePrice.ToString("F2", CultureInfo.InvariantCulture),
                EscapeCsvField(transaction.PurchaseDate.ToString("yyyy-MM-dd HH:mm:ss")),
                EscapeCsvField(transaction.TransactionId ?? ""),
                EscapeCsvField(transaction.PaymentMethod ?? ""),
                EscapeCsvField(transaction.Notes ?? "")
            });
        }

        private string FormatAttemptForCsv(PurchaseAttempt attempt)
        {
            return string.Join(",", new[]
            {
                attempt.Id.ToString(),
                EscapeCsvField(attempt.ItemId),
                EscapeCsvField(attempt.AttemptDate.ToString("yyyy-MM-dd HH:mm:ss")),
                EscapeCsvField(attempt.Result),
                EscapeCsvField(attempt.ErrorMessage ?? "")
            });
        }

        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            // If field contains comma, newline, or quote, wrap in quotes and escape internal quotes
            if (field.Contains(",") || field.Contains("\n") || field.Contains("\""))
            {
                return "\"" + field.Replace("\"", "\"\"") + "\"";
            }

            return field;
        }

        private string MakeFilenameSafe(string filename)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var safeName = new string(filename.Where(c => !invalidChars.Contains(c)).ToArray());
            return safeName.Length > 50 ? safeName.Substring(0, 50) : safeName;
        }
    }
}
