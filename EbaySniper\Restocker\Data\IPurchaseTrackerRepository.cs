using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Data
{
    /// <summary>
    /// Interface for purchase tracker data access operations
    /// </summary>
    public interface IPurchaseTrackerRepository : IDisposable
    {
        /// <summary>
        /// Initialize the database and create tables if they don't exist
        /// </summary>
        Task InitializeDatabaseAsync();

        // Purchase Quantity Tracking
        Task<int> GetPurchasedQuantityAsync(string keywordId, string jobId);

        // Purchase Transactions
        Task<IEnumerable<PurchaseTransaction>> GetTransactionsByKeywordAndJobAsync(string keywordId, string jobId);
        Task<IEnumerable<PurchaseTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<int> AddTransactionAsync(PurchaseTransaction transaction);
        Task<bool> UpdateTransactionStatusAsync(int transactionId, string status);

        // Purchase Attempts
        Task<IEnumerable<PurchaseAttempt>> GetAttemptsByKeywordAndJobAsync(string keywordId, string jobId);
        Task<int> AddAttemptAsync(PurchaseAttempt attempt);

        // Sync History
        Task<IEnumerable<SyncHistory>> GetSyncHistoryAsync(int limit = 50);
        Task<int> AddSyncHistoryAsync(SyncHistory syncHistory);

        // Configuration
        Task<string> GetConfigurationValueAsync(string key);
        Task SetConfigurationValueAsync(string key, string value);

        // Additional methods for reporting
        Task<IEnumerable<PurchaseTransaction>> GetAllTransactionsAsync();
    }
}
