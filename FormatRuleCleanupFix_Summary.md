# Format Rule Cleanup Fix - Summary

## Problem Description
When changing a filter action from "Format Row" or "Format Cell" to another action type (like "Remove rows"), the old formatting rules remained applied to the grid. The rows would still show the old formatting appearance even though the filter action had changed.

## Root Cause Analysis
The issue was in the `XFilterManager.cs` file in two key methods:

1. **`AddOrReplaceFilter` method (line 343)** - Used by the legacy filter system
2. **`ApplyFilterAsync` method (line 288)** - Used by the new action system

Both methods only handled adding/replacing format rules when the current action was a formatting action, but they didn't remove old format rules when switching from formatting to non-formatting actions.

## Solution Implemented

### 1. Enhanced `AddOrReplaceFilter` Method
**File**: `EbaySniper/Filters/XFilterManager.cs` (lines 368-399)

**Key Changes**:
- Added logic to find existing format rules by filter ID (using `GridFormatRule.Tag`)
- Added check to determine if current action is formatting or non-formatting
- Added cleanup logic to remove old format rules when switching to non-formatting actions

```csharp
// Find any existing format rule for this filter
var existingRuleIndex = grView.FormatRules
    .FindIndex(fr => (fr.Tag is Guid tag ? tag : default) == xFilter.Id);

// Check if the current action is a formatting action
var isFormattingAction = xFilter.Action.Contains("Format rows") || xFilter.Action.Contains("Format cells");

if (isFormattingAction)
{
    // Handle formatting actions - add or replace format rule
    // ... existing logic ...
}
else
{
    // Handle non-formatting actions - remove any existing format rule
    if (existingRuleIndex >= 0)
    {
        grView.FormatRules.RemoveAt(existingRuleIndex);
    }
}
```

### 2. Enhanced `ApplyFilterAsync` Method
**File**: `EbaySniper/Filters/XFilterManager.cs` (lines 306-316)

**Key Changes**:
- Added similar cleanup logic for the new action system
- Uses type checking (`FormatRowsAction` or `FormatCellsAction`) instead of string matching

```csharp
// Handle format rule cleanup for action changes
var existingRuleIndex = grView.FormatRules
    .FindIndex(fr => (fr.Tag is Guid tag ? tag : default) == xFilter.Id);

var isFormattingAction = xFilter.ActionHandler is FormatRowsAction or FormatCellsAction;

if (!isFormattingAction && existingRuleIndex >= 0)
{
    // Remove existing format rule when switching from formatting to non-formatting action
    grView.FormatRules.RemoveAt(existingRuleIndex);
}
```

## How the Fix Works

1. **Detection**: Both methods now detect if there's an existing format rule for the current filter by looking for a `GridFormatRule` with a `Tag` that matches the filter's ID.

2. **Action Type Check**: They determine if the current action is a formatting action:
   - Legacy system: Checks if action string contains "Format rows" or "Format cells"
   - New system: Checks if `ActionHandler` is `FormatRowsAction` or `FormatCellsAction`

3. **Cleanup Logic**: 
   - If the action is formatting: Add or replace the format rule (existing behavior)
   - If the action is NOT formatting but there's an existing format rule: Remove the old format rule

## Testing the Fix

### Manual Testing Steps:
1. Create a new filter with "Format Row" or "Format Cell" action
2. Set up some color formatting (e.g., red background)
3. Apply the filter and verify the formatting appears on matching rows
4. Edit the same filter and change the action to "Remove rows" or any other non-formatting action
5. Save the filter changes
6. **Expected Result**: The old formatting should be automatically removed from the grid

### Unit Tests Created
**File**: `uBuyFirst.Tests/Filters/FormatRuleCleanupTests.cs`

The following test scenarios are covered:
- `AddOrReplaceFilter_WhenChangingFromFormatToNonFormat_RemovesOldFormatRule`
- `AddOrReplaceFilter_WhenChangingFromNonFormatToFormat_AddsFormatRule`
- `AddOrReplaceFilter_WhenReplacingFormatRule_MaintainsSamePosition`
- `AddOrReplaceFilter_WithNonFormattingAction_DoesNotAddFormatRule`
- `ApplyFilterAsync_WhenChangingFromFormatToNonFormat_RemovesOldFormatRule`
- `ApplyFilterAsync_WithFormattingAction_DoesNotRemoveFormatRule`

## Backward Compatibility
- The fix is fully backward compatible
- No existing functionality is affected
- Only adds the missing cleanup logic that was causing the issue
- Works with both legacy and new action systems

## Files Modified
1. `EbaySniper/Filters/XFilterManager.cs` - Core fix implementation
2. `uBuyFirst.Tests/Filters/FormatRuleCleanupTests.cs` - Unit tests (new file)

## Impact
- **User Experience**: Filters now behave as expected when changing action types
- **Performance**: Minimal impact - only adds a few lines of cleanup logic
- **Maintenance**: Cleaner code with proper resource cleanup
- **Reliability**: Prevents visual inconsistencies in the grid formatting
