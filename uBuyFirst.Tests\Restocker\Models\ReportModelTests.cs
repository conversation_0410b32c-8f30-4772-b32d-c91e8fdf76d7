﻿using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Tests.Restocker.Models
{
    [TestClass]
    public class ReportModelTests
    {
        [TestMethod]
        public void TransactionDetailReport_Constructor_SetsDefaultValues()
        {
            // Act
            var report = new TransactionDetailReport();

            // Assert
            Assert.IsNotNull(report.KeywordId);
            Assert.IsNotNull(report.JobId);
            Assert.IsNotNull(report.KeywordAlias);
            Assert.IsNotNull(report.Transactions);
            Assert.IsNotNull(report.Attempts);
            Assert.AreEqual(0, report.Transactions.Count);
            Assert.AreEqual(0, report.Attempts.Count);
        }

        [TestMethod]
        public void TransactionDetailReport_WithData_PopulatesCorrectly()
        {
            // Arrange
            var transactions = new List<PurchaseTransaction>
            {
                new PurchaseTransaction
                {
                    Id = 1,
                    KeywordId = "test-keyword",
                    JobId = "job-123",
                    ItemId = "item-456",
                    Status = "Completed",
                    Quantity = 2,
                    PurchasePrice = 25.99m
                }
            };

            var attempts = new List<PurchaseAttempt>
            {
                new PurchaseAttempt
                {
                    Id = 1,
                    KeywordId = "test-keyword",
                    JobId = "job-123",
                    ItemId = "item-456",
                    Result = "Success"
                }
            };

            // Act
            var report = new TransactionDetailReport
            {
                KeywordId = "test-keyword",
                JobId = "job-123",
                KeywordAlias = "Test Product",
                Transactions = transactions,
                Attempts = attempts
            };

            // Assert
            Assert.AreEqual("test-keyword", report.KeywordId);
            Assert.AreEqual("job-123", report.JobId);
            Assert.AreEqual("Test Product", report.KeywordAlias);
            Assert.AreEqual(1, report.Transactions.Count);
            Assert.AreEqual(1, report.Attempts.Count);
            Assert.AreEqual("Completed", report.Transactions[0].Status);
            Assert.AreEqual("Success", report.Attempts[0].Result);
        }

        [TestMethod]
        public void UserReport_Constructor_SetsDefaultValues()
        {
            // Act
            var report = new UserReport();

            // Assert
            Assert.IsNotNull(report.KeywordId);
            Assert.IsNotNull(report.JobId);
            Assert.IsNotNull(report.KeywordAlias);
            Assert.IsNotNull(report.Keywords);
            Assert.AreEqual(0, report.RequiredQuantity);
            Assert.AreEqual(0, report.PurchasedQuantity);
            Assert.AreEqual(0, report.PriceMin);
            Assert.AreEqual(0, report.PriceMax);
        }

        [TestMethod]
        public void UserReport_WithKeywordData_PopulatesAllFields()
        {
            // Arrange
            var keyword = new Keyword2Find
            {
                Id = "test-keyword",
                Alias = "Test Product",
                Kws = "test,product,keywords",
                JobId = "job-123",
                RequiredQuantity = 5,
                PurchasedQuantity = 3,
                PriceMin = 10.0,
                PriceMax = 50.0,
                Categories4Api = "12345",
                Sellers = new string[] { "seller1", "seller2" },
                SellerType = "Include",
                EbaySiteName = "eBay US"
            };

            // Act
            var report = new UserReport();
            report.PopulateFromKeyword(keyword);

            // Assert
            Assert.AreEqual("test-keyword", report.KeywordId);
            Assert.AreEqual("job-123", report.JobId);
            Assert.AreEqual("Test Product", report.KeywordAlias);
            Assert.AreEqual("test,product,keywords", report.Keywords);
            Assert.AreEqual(5, report.RequiredQuantity);
            Assert.AreEqual(3, report.PurchasedQuantity);
            Assert.AreEqual(10.0, report.PriceMin);
            Assert.AreEqual(50.0, report.PriceMax);
            Assert.AreEqual("12345", report.Categories);
            Assert.AreEqual("seller1,seller2", report.Sellers);
            Assert.AreEqual("Include", report.SellerType);
            Assert.AreEqual("eBay US", report.EbaySite);
        }

        [TestMethod]
        public void UserReport_WithTransactionData_PopulatesLastOrderInfo()
        {
            // Arrange
            var report = new UserReport();
            var lastTransaction = new PurchaseTransaction
            {
                Status = "Completed",
                PurchaseDate = DateTime.Now.AddHours(-1),
                LastStepHtml = "<html>Success page</html>"
            };

            // Act
            report.UpdateFromLastTransaction(lastTransaction);

            // Assert
            Assert.AreEqual("Completed", report.LastOrderStatus);
            Assert.AreEqual(lastTransaction.PurchaseDate, report.LastTransactionTime);
            Assert.AreEqual("<html>Success page</html>", report.LastStepHtml);
        }

        [TestMethod]
        public void ReportFilter_Constructor_SetsDefaultValues()
        {
            // Act
            var filter = new ReportFilter();

            // Assert
            Assert.IsNull(filter.StartDate);
            Assert.IsNull(filter.EndDate);
            Assert.IsNull(filter.JobId);
            Assert.IsNull(filter.KeywordId);
            Assert.IsTrue(filter.IncludeCompleted);
            Assert.IsTrue(filter.IncludeFailed);
            Assert.IsTrue(filter.IncludePending);
        }

        [TestMethod]
        public void ReportFilter_WithDateRange_ValidatesCorrectly()
        {
            // Arrange
            var startDate = DateTime.Now.AddDays(-7);
            var endDate = DateTime.Now;

            // Act
            var filter = new ReportFilter
            {
                StartDate = startDate,
                EndDate = endDate
            };

            // Assert
            Assert.AreEqual(startDate, filter.StartDate);
            Assert.AreEqual(endDate, filter.EndDate);
            Assert.IsTrue(filter.IsValid());
        }

        [TestMethod]
        public void ReportFilter_WithInvalidDateRange_FailsValidation()
        {
            // Arrange
            var startDate = DateTime.Now;
            var endDate = DateTime.Now.AddDays(-7);

            // Act
            var filter = new ReportFilter
            {
                StartDate = startDate,
                EndDate = endDate
            };

            // Assert
            Assert.IsFalse(filter.IsValid());
        }
    }
}
