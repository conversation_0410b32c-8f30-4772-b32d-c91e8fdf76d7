using System.Collections.Generic;
using System.Threading.Tasks;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Interface for generating reports from Restocker data
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// Generates a detailed transaction report for a specific keyword and job
        /// </summary>
        /// <param name="keywordId">The keyword ID to generate report for</param>
        /// <param name="jobId">The job ID to generate report for</param>
        /// <returns>Detailed transaction report</returns>
        Task<TransactionDetailReport> GenerateTransactionDetailReportAsync(string keywordId, string jobId);

        /// <summary>
        /// Generates user reports based on the provided filter
        /// </summary>
        /// <param name="filter">Filter criteria for the report</param>
        /// <returns>Collection of user reports</returns>
        Task<IEnumerable<UserReport>> GenerateUserReportAsync(ReportFilter filter);

        /// <summary>
        /// Gets summary statistics for all purchase activities
        /// </summary>
        /// <param name="filter">Optional filter criteria</param>
        /// <returns>Summary statistics</returns>
        Task<ReportSummary> GetReportSummaryAsync(ReportFilter filter = null);
    }

    /// <summary>
    /// Summary statistics for reporting
    /// </summary>
    public class ReportSummary
    {
        public int TotalTransactions { get; set; }
        public int CompletedTransactions { get; set; }
        public int FailedTransactions { get; set; }
        public int PendingTransactions { get; set; }
        public decimal TotalAmountSpent { get; set; }
        public int TotalQuantityPurchased { get; set; }
        public int UniqueJobIds { get; set; }
        public int UniqueKeywordIds { get; set; }
    }
}
