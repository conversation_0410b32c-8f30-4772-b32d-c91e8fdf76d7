using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.IO;
using System.Threading.Tasks;
using uBuyFirst.Other;
using uBuyFirst.Prefs;

namespace uBuyFirst.Tests
{
    [TestClass]
    [TestCategory("DatabaseInitialization")]
    public class Form1DatabaseInitializationTests
    {
        private bool _originalRestockerEnabledValue;
        private string _originalSettingsFolder;
        private string _tempTestFolder;

        [TestInitialize]
        public void Setup()
        {
            // Store original values to restore later
            _originalRestockerEnabledValue = ConnectionConfig.RestockerEnabled;
            _originalSettingsFolder = Folders.Settings;

            // Create a temporary test folder structure
            _tempTestFolder = Path.Combine(Path.GetTempPath(), $"RestockTest_{Guid.NewGuid()}");
            Directory.CreateDirectory(_tempTestFolder);

            // Set up test environment
            typeof(Folders).GetField("Settings", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
                ?.SetValue(null, _tempTestFolder);
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Restore original values
            ConnectionConfig.RestockerEnabled = _originalRestockerEnabledValue;
            typeof(Folders).GetField("Settings", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
                ?.SetValue(null, _originalSettingsFolder);

            // Force garbage collection to release any SQLite connections
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            // Clean up test folder with retry logic for locked files
            if (Directory.Exists(_tempTestFolder))
            {
                try
                {
                    Directory.Delete(_tempTestFolder, true);
                }
                catch (IOException)
                {
                    // If files are locked, wait a bit and try again
                    System.Threading.Thread.Sleep(100);
                    try
                    {
                        Directory.Delete(_tempTestFolder, true);
                    }
                    catch (IOException)
                    {
                        // If still locked, just leave it - the temp folder will be cleaned up eventually
                        // This prevents test failures due to file locking issues
                    }
                }
            }
        }

        [TestMethod]
        public void InitializeAutoPurchaseSystemVisibility_WhenRestockerEnabled_InitializesDatabase()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;
            var restockFolder = Path.Combine(_tempTestFolder, "Restock");
            var expectedDbPath = Path.Combine(restockFolder, "restock.db");
            var connectionString = $"Data Source={expectedDbPath};Version=3;";

            // Create a minimal Form1 instance for testing
            // Note: This is a simplified test - in practice, Form1 has complex dependencies
            // We're testing the concept that database initialization should happen during startup

            // Act - Simulate what happens in InitializeAutoPurchaseSystemVisibility
            if (ConnectionConfig.RestockerEnabled)
            {
                // This simulates the database initialization that should happen
                using var repository = new uBuyFirst.Restocker.Data.PurchaseTrackerRepository(connectionString);
                var task = repository.InitializeDatabaseAsync();
                task.Wait(); // Wait for completion in test
            }

            // Assert
            Assert.IsTrue(Directory.Exists(restockFolder), "Restock folder should be created");
            Assert.IsTrue(File.Exists(expectedDbPath), "restock.db should be created when RestockerEnabled is true");
        }

        [TestMethod]
        public void InitializeAutoPurchaseSystemVisibility_WhenRestockerDisabled_DoesNotInitializeDatabase()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;
            var restockFolder = Path.Combine(_tempTestFolder, "Restock");
            var expectedDbPath = Path.Combine(restockFolder, "restock.db");

            // Act - Simulate what happens in InitializeAutoPurchaseSystemVisibility
            // When RestockerEnabled is false, database should not be initialized

            // Assert
            Assert.IsFalse(Directory.Exists(restockFolder), "Restock folder should not be created when RestockerEnabled is false");
            Assert.IsFalse(File.Exists(expectedDbPath), "restock.db should not be created when RestockerEnabled is false");
        }

        [TestMethod]
        public async Task DatabaseInitialization_CreatesCorrectPath()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;
            var restockFolder = Path.Combine(_tempTestFolder, "Restock");
            var expectedDbPath = Path.Combine(restockFolder, "restock.db");
            var connectionString = $"Data Source={expectedDbPath};Version=3;";

            // Act
            using var repository = new uBuyFirst.Restocker.Data.PurchaseTrackerRepository(connectionString);
            await repository.InitializeDatabaseAsync();

            // Assert
            Assert.IsTrue(Directory.Exists(restockFolder), "Restock folder should be created");
            Assert.IsTrue(File.Exists(expectedDbPath), "Database should be created at the correct path");

            // Verify database has the expected structure
            using (var connection = new System.Data.SQLite.SQLiteConnection(connectionString))
            {
                connection.Open();

                // Check that tables exist - verify the new schema with KeywordId
                using (var command = new System.Data.SQLite.SQLiteCommand(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='PurchaseAttempts';",
                    connection))
                {
                    var result = command.ExecuteScalar();
                    Assert.IsNotNull(result, "PurchaseAttempts table should exist");
                    Assert.AreEqual("PurchaseAttempts", result.ToString());
                }

                // Verify that PurchaseAttempts table has KeywordId column (new schema)
                using (var columnCheckCommand = new System.Data.SQLite.SQLiteCommand(
                    "PRAGMA table_info(PurchaseAttempts);",
                    connection))
                {
                    using var reader = columnCheckCommand.ExecuteReader();
                    bool hasKeywordId = false;
                    bool hasJobId = false;
                    while (reader.Read())
                    {
                        var columnName = reader.GetString(1); // Column name is at index 1
                        if (columnName == "KeywordId") hasKeywordId = true;
                        if (columnName == "JobId") hasJobId = true;
                    }

                    Assert.IsTrue(hasKeywordId, "PurchaseAttempts table should have KeywordId column");
                    Assert.IsTrue(hasJobId, "PurchaseAttempts table should have JobId column");
                }
            }
        }

        [TestMethod]
        public void DatabasePath_MatchesRequirements()
        {
            // Arrange
            var expectedDbPath = Path.Combine(_tempTestFolder, "Restock", "restock.db");
            var expectedConnectionString = $"Data Source={expectedDbPath};Version=3;";

            // Act
            using var repository = new uBuyFirst.Restocker.Data.PurchaseTrackerRepository(expectedConnectionString);

            // Use reflection to get the connection string and verify the path
            var connectionStringField = typeof(uBuyFirst.Restocker.Data.PurchaseTrackerRepository)
                .GetField("_connectionString", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var connectionString = connectionStringField?.GetValue(repository) as string;

            // Assert
            Assert.IsNotNull(connectionString, "Connection string should not be null");
            Assert.IsTrue(connectionString.Contains("restock.db"), "Connection string should reference restock.db");
            Assert.IsTrue(connectionString.Contains("Restock"), "Connection string should reference Restock folder");
            Assert.IsTrue(connectionString.Contains(_tempTestFolder), "Connection string should use the Settings folder path");
        }
    }
}
