using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class RestockerReportingManagerTests
    {
        private Mock<IReportService> _mockReportService;
        private Mock<IReportExportService> _mockExportService;
        private RestockerReportingManager _reportingManager;
        private string _testOutputDirectory;

        [TestInitialize]
        public void Setup()
        {
            _mockReportService = new Mock<IReportService>();
            _mockExportService = new Mock<IReportExportService>();
            _reportingManager = new RestockerReportingManager(_mockReportService.Object, _mockExportService.Object);
            
            _testOutputDirectory = Path.Combine(Path.GetTempPath(), "RestockerReportingTests");
            Directory.CreateDirectory(_testOutputDirectory);
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (Directory.Exists(_testOutputDirectory))
            {
                Directory.Delete(_testOutputDirectory, true);
            }
        }

        [TestMethod]
        public async Task GenerateMonthlyUserReportAsync_WithValidData_CreatesReport()
        {
            // Arrange
            var userReports = new List<UserReport>
            {
                new UserReport
                {
                    KeywordId = "keyword-1",
                    JobId = "job-123",
                    KeywordAlias = "Test Product",
                    RequiredQuantity = 5,
                    PurchasedQuantity = 3
                }
            };

            _mockReportService.Setup(s => s.GenerateUserReportAsync(It.IsAny<ReportFilter>()))
                             .ReturnsAsync(userReports);
            
            _mockExportService.Setup(s => s.ExportUserReportToCsvAsync(It.IsAny<IEnumerable<UserReport>>(), It.IsAny<string>()))
                             .Returns(Task.CompletedTask);

            // Act
            var reportPath = await _reportingManager.GenerateMonthlyUserReportAsync(_testOutputDirectory);

            // Assert
            Assert.IsNotNull(reportPath);
            Assert.IsTrue(reportPath.Contains("restocker_user_report_"));
            Assert.IsTrue(reportPath.EndsWith(".csv"));
            
            _mockReportService.Verify(s => s.GenerateUserReportAsync(It.Is<ReportFilter>(f => 
                f.StartDate.HasValue && 
                f.EndDate.HasValue && 
                f.IncludeCompleted && 
                f.IncludeFailed && 
                f.IncludePending)), Times.Once);
            
            _mockExportService.Verify(s => s.ExportUserReportToCsvAsync(userReports, reportPath), Times.Once);
        }

        [TestMethod]
        public async Task GenerateJobDetailReportAsync_WithValidData_CreatesReport()
        {
            // Arrange
            var keywordId = "keyword-123";
            var jobId = "job-456";
            var detailReport = new TransactionDetailReport
            {
                KeywordId = keywordId,
                JobId = jobId,
                KeywordAlias = "Test Product"
            };

            _mockReportService.Setup(s => s.GenerateTransactionDetailReportAsync(keywordId, jobId))
                             .ReturnsAsync(detailReport);
            
            _mockExportService.Setup(s => s.ExportTransactionDetailReportToCsvAsync(It.IsAny<TransactionDetailReport>(), It.IsAny<string>()))
                             .Returns(Task.CompletedTask);

            // Act
            var reportPath = await _reportingManager.GenerateJobDetailReportAsync(keywordId, jobId, _testOutputDirectory);

            // Assert
            Assert.IsNotNull(reportPath);
            Assert.IsTrue(reportPath.Contains($"restocker_job_detail_{keywordId}_{jobId}_"));
            Assert.IsTrue(reportPath.EndsWith(".csv"));
            
            _mockReportService.Verify(s => s.GenerateTransactionDetailReportAsync(keywordId, jobId), Times.Once);
            _mockExportService.Verify(s => s.ExportTransactionDetailReportToCsvAsync(detailReport, reportPath), Times.Once);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public async Task GenerateJobDetailReportAsync_WithEmptyKeywordId_ThrowsException()
        {
            // Act
            await _reportingManager.GenerateJobDetailReportAsync("", "job-123");
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public async Task GenerateJobDetailReportAsync_WithEmptyJobId_ThrowsException()
        {
            // Act
            await _reportingManager.GenerateJobDetailReportAsync("keyword-123", "");
        }

        [TestMethod]
        public async Task GenerateCustomUserReportAsync_WithValidFilter_CreatesReport()
        {
            // Arrange
            var filter = new ReportFilter
            {
                StartDate = DateTime.Now.AddDays(-7),
                EndDate = DateTime.Now,
                JobId = "job-123"
            };

            var userReports = new List<UserReport>
            {
                new UserReport { KeywordId = "keyword-1", JobId = "job-123" }
            };

            _mockReportService.Setup(s => s.GenerateUserReportAsync(filter))
                             .ReturnsAsync(userReports);
            
            _mockExportService.Setup(s => s.ExportUserReportToCsvAsync(It.IsAny<IEnumerable<UserReport>>(), It.IsAny<string>()))
                             .Returns(Task.CompletedTask);

            // Act
            var reportPath = await _reportingManager.GenerateCustomUserReportAsync(filter, _testOutputDirectory);

            // Assert
            Assert.IsNotNull(reportPath);
            Assert.IsTrue(reportPath.Contains("restocker_custom_report_"));
            Assert.IsTrue(reportPath.EndsWith(".csv"));
            
            _mockReportService.Verify(s => s.GenerateUserReportAsync(filter), Times.Once);
            _mockExportService.Verify(s => s.ExportUserReportToCsvAsync(userReports, reportPath), Times.Once);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public async Task GenerateCustomUserReportAsync_WithNullFilter_ThrowsException()
        {
            // Act
            await _reportingManager.GenerateCustomUserReportAsync(null);
        }

        [TestMethod]
        public async Task SavePurchaseStepHtmlAsync_WithValidData_SavesHtml()
        {
            // Arrange
            var htmlContent = "<html><body>Success</body></html>";
            var keywordId = "keyword-123";
            var jobId = "job-456";
            var expectedPath = "/path/to/saved/file.html";

            _mockExportService.Setup(s => s.SaveHtmlContentAsync(htmlContent, keywordId, jobId, It.IsAny<DateTime>()))
                             .ReturnsAsync(expectedPath);

            // Act
            var result = await _reportingManager.SavePurchaseStepHtmlAsync(htmlContent, keywordId, jobId);

            // Assert
            Assert.AreEqual(expectedPath, result);
            _mockExportService.Verify(s => s.SaveHtmlContentAsync(htmlContent, keywordId, jobId, It.IsAny<DateTime>()), Times.Once);
        }

        [TestMethod]
        public async Task GetPurchaseSummaryAsync_WithFilter_ReturnsSummary()
        {
            // Arrange
            var filter = new ReportFilter { StartDate = DateTime.Now.AddDays(-30) };
            var expectedSummary = new ReportSummary
            {
                TotalTransactions = 10,
                CompletedTransactions = 8,
                FailedTransactions = 2,
                TotalAmountSpent = 250.50m
            };

            _mockReportService.Setup(s => s.GetReportSummaryAsync(filter))
                             .ReturnsAsync(expectedSummary);

            // Act
            var result = await _reportingManager.GetPurchaseSummaryAsync(filter);

            // Assert
            Assert.AreEqual(expectedSummary, result);
            _mockReportService.Verify(s => s.GetReportSummaryAsync(filter), Times.Once);
        }

        [TestMethod]
        public async Task CleanupOldHtmlFilesAsync_WithRetentionDays_ReturnsDeletedCount()
        {
            // Arrange
            var retentionDays = 60;
            var expectedDeletedCount = 5;

            _mockExportService.Setup(s => s.CleanupOldHtmlFilesAsync(retentionDays))
                             .ReturnsAsync(expectedDeletedCount);

            // Act
            var result = await _reportingManager.CleanupOldHtmlFilesAsync(retentionDays);

            // Assert
            Assert.AreEqual(expectedDeletedCount, result);
            _mockExportService.Verify(s => s.CleanupOldHtmlFilesAsync(retentionDays), Times.Once);
        }

        [TestMethod]
        public void GetHtmlStorageDirectory_ReturnsDirectory()
        {
            // Arrange
            var expectedDirectory = "/path/to/html/storage";
            _mockExportService.Setup(s => s.GetHtmlStorageDirectory())
                             .Returns(expectedDirectory);

            // Act
            var result = _reportingManager.GetHtmlStorageDirectory();

            // Assert
            Assert.AreEqual(expectedDirectory, result);
            _mockExportService.Verify(s => s.GetHtmlStorageDirectory(), Times.Once);
        }

        [TestMethod]
        public async Task GenerateComprehensiveReportPackageAsync_CreatesMultipleReports()
        {
            // Arrange
            var userReports = new List<UserReport>
            {
                new UserReport { KeywordId = "keyword-1", JobId = "job-123" }
            };

            var summary = new ReportSummary
            {
                TotalTransactions = 5,
                CompletedTransactions = 4,
                FailedTransactions = 1
            };

            _mockReportService.Setup(s => s.GenerateUserReportAsync(It.IsAny<ReportFilter>()))
                             .ReturnsAsync(userReports);
            
            _mockReportService.Setup(s => s.GetReportSummaryAsync(null))
                             .ReturnsAsync(summary);
            
            _mockExportService.Setup(s => s.ExportUserReportToCsvAsync(It.IsAny<IEnumerable<UserReport>>(), It.IsAny<string>()))
                             .Returns(Task.CompletedTask);

            // Act
            var reportPaths = await _reportingManager.GenerateComprehensiveReportPackageAsync(_testOutputDirectory);

            // Assert
            Assert.IsNotNull(reportPaths);
            Assert.AreEqual(2, reportPaths.Count);
            Assert.IsTrue(reportPaths.Any(p => p.Contains("restocker_user_report_")));
            Assert.IsTrue(reportPaths.Any(p => p.Contains("restocker_summary_")));
            
            _mockReportService.Verify(s => s.GenerateUserReportAsync(It.IsAny<ReportFilter>()), Times.Once);
            _mockReportService.Verify(s => s.GetReportSummaryAsync(null), Times.Once);
            _mockExportService.Verify(s => s.ExportUserReportToCsvAsync(userReports, It.IsAny<string>()), Times.Once);
        }
    }
}
