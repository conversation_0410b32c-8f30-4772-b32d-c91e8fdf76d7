using System;

namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents a user-friendly report containing all spreadsheet data and purchase information
    /// </summary>
    public class UserReport
    {
        // Core identification
        public string KeywordId { get; set; } = string.Empty;
        public string JobId { get; set; } = string.Empty;
        public string KeywordAlias { get; set; } = string.Empty;

        // Spreadsheet data (all cells from a row)
        public string Keywords { get; set; } = string.Empty;
        public bool SearchInDescription { get; set; }
        public double PriceMin { get; set; }
        public double PriceMax { get; set; }
        public string Categories { get; set; } = string.Empty;
        public string Condition { get; set; } = string.Empty;
        public string EbaySite { get; set; } = string.Empty;
        public string LocatedIn { get; set; } = string.Empty;
        public string ShipsTo { get; set; } = string.Empty;
        public string ShipZipcode { get; set; } = string.Empty;
        public string Sellers { get; set; } = string.Empty;
        public string SellerType { get; set; } = string.Empty;
        public TimeSpan Interval { get; set; }
        public int Threads { get; set; }
        public string ViewName { get; set; } = string.Empty;
        public string ListingType { get; set; } = string.Empty;

        // Purchase requirement data
        public int RequiredQuantity { get; set; }
        public int PurchasedQuantity { get; set; }

        // Last order information
        public string LastOrderStatus { get; set; } = string.Empty;
        public DateTime? LastTransactionTime { get; set; }
        public string LastStepHtml { get; set; } = string.Empty;

        // Report metadata
        public DateTime ReportGeneratedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Populates the report from a Keyword2Find object (all spreadsheet cells)
        /// </summary>
        /// <param name="keyword">The keyword object containing spreadsheet data</param>
        public void PopulateFromKeyword(Keyword2Find keyword)
        {
            if (keyword == null) return;

            KeywordId = keyword.Id;
            JobId = keyword.JobId;
            KeywordAlias = keyword.Alias;
            Keywords = keyword.Kws;
            SearchInDescription = keyword.SearchInDescription;
            PriceMin = keyword.PriceMin;
            PriceMax = keyword.PriceMax;
            Categories = keyword.Categories4Api;
            Condition = string.Join(",", keyword.Condition);
            EbaySite = keyword.EbaySiteName;
            LocatedIn = keyword.LocatedIn;
            ShipsTo = keyword.AvailableTo;
            ShipZipcode = keyword.Zip;
            Sellers = keyword.SellersStr;
            SellerType = keyword.SellerType;
            Interval = keyword.Frequency;
            Threads = keyword.Threads;
            ViewName = keyword.ViewName;
            ListingType = string.Join(",", keyword.ListingType);
            RequiredQuantity = keyword.RequiredQuantity;
            PurchasedQuantity = keyword.PurchasedQuantity;
        }

        /// <summary>
        /// Updates the report with information from the last transaction
        /// </summary>
        /// <param name="lastTransaction">The most recent transaction</param>
        public void UpdateFromLastTransaction(PurchaseTransaction lastTransaction)
        {
            if (lastTransaction == null) return;

            LastOrderStatus = lastTransaction.Status;
            LastTransactionTime = lastTransaction.PurchaseDate;
            LastStepHtml = lastTransaction.LastStepHtml ?? string.Empty;
        }

        /// <summary>
        /// Gets the remaining quantity needed
        /// </summary>
        public int RemainingQuantity => Math.Max(0, RequiredQuantity - PurchasedQuantity);

        /// <summary>
        /// Gets the completion percentage
        /// </summary>
        public double CompletionPercentage => RequiredQuantity > 0 ? (double)PurchasedQuantity / RequiredQuantity * 100 : 0;

        /// <summary>
        /// Indicates if the purchase requirement is fulfilled
        /// </summary>
        public bool IsCompleted => PurchasedQuantity >= RequiredQuantity;
    }
}
