﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
// See the LICENSE file in the project root for more information.

using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraLayout;

namespace uBuyFirst
{
    partial class FormExternalData
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.btnPreview = new DevExpress.XtraEditors.SimpleButton();
            this.layoutControl3 = new DevExpress.XtraLayout.LayoutControl();
            this.connectionTypeStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.connectionTypeHorStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.chkExternalDataEnabled = new DevExpress.XtraEditors.CheckEdit();
            this.lblConnectionType = new DevExpress.XtraEditors.LabelControl();
            this.radioExternalDataGroups = new DevExpress.XtraEditors.RadioGroup();
            this.ribbon = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.pythonInstallStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.installPythonLinkButton = new DevExpress.XtraEditors.SimpleButton();
            this.installPythonLibsButton = new DevExpress.XtraEditors.SimpleButton();
            this.lblSpace = new DevExpress.XtraEditors.LabelControl();
            this.stepsAndStartStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.scriptStepsTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.scriptStepsTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.csvAllstackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.lblStep1Local = new DevExpress.XtraEditors.LabelControl();
            this.browseCsvStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.lblCsvFile = new DevExpress.XtraEditors.LabelControl();
            this.txtCsvFileName = new DevExpress.XtraEditors.TextEdit();
            this.browseButtonCsv = new DevExpress.XtraEditors.SimpleButton();
            this.csvstackPanelMatchingScript = new DevExpress.Utils.Layout.StackPanel();
            this.stackPanel2 = new DevExpress.Utils.Layout.StackPanel();
            this.lblCsvFieldsToMatch = new DevExpress.XtraEditors.LabelControl();
            this.csvFieldsToUseForMatchListBoxControl = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.SaveAndContCsvButton = new DevExpress.XtraEditors.SimpleButton();
            this.scriptStepsTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.step2StackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.lblStep2Local = new DevExpress.XtraEditors.LabelControl();
            this.sentItemsStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.stacklistFields = new DevExpress.Utils.Layout.StackPanel();
            this.lblStdListFields = new DevExpress.XtraEditors.LabelControl();
            this.chkListUrlParams = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.stackCategoryFields = new DevExpress.Utils.Layout.StackPanel();
            this.lblCategoryItemSpecList = new DevExpress.XtraEditors.LabelControl();
            this.chkListUrlParamsCategories = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.sendFieldsDescSaveContStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.chkSendDescriptionAndPictures = new DevExpress.XtraEditors.CheckEdit();
            this.SaveAndContFieldSenderButton = new DevExpress.XtraEditors.SimpleButton();
            this.scriptStepsTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.step3stackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.lblMatchingScript = new DevExpress.XtraEditors.LabelControl();
            this.matchingScriptEdit = new DevExpress.XtraEditors.MemoEdit();
            this.SaveAndContMatchScriptButton = new DevExpress.XtraEditors.SimpleButton();
            this.scriptStepsTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.step4stackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.lblTemplateScript = new DevExpress.XtraEditors.LabelControl();
            this.matchingTemplateEdit = new DevExpress.XtraEditors.MemoEdit();
            this.SaveAndContTemplateButton = new DevExpress.XtraEditors.SimpleButton();
            this.scriptAITabPage = new DevExpress.XtraTab.XtraTabPage();
            this.saveAIConfigurationButton = new DevExpress.XtraEditors.SimpleButton();
            this.modelComboBox = new System.Windows.Forms.ComboBox();
            this.aiProviderComboBox = new System.Windows.Forms.ComboBox();
            this.lblModel = new DevExpress.XtraEditors.LabelControl();
            this.txtApiKey = new DevExpress.XtraEditors.TextEdit();
            this.aiPromptEdit = new DevExpress.XtraEditors.MemoEdit();
            this.lblAiPrompt = new DevExpress.XtraEditors.LabelControl();
            this.lblApiKey = new DevExpress.XtraEditors.LabelControl();
            this.lblAIProviderDropdown = new DevExpress.XtraEditors.LabelControl();
            this.scriptAIDisplayPage = new DevExpress.XtraTab.XtraTabPage();
            this.saveAIConfigurationDisplayButton = new DevExpress.XtraEditors.SimpleButton();
            this.memoEditAIDisplayTemplate = new DevExpress.XtraEditors.MemoEdit();
            this.tabAiColumns = new DevExpress.XtraTab.XtraTabPage();
            this.layoutControlAiColumns = new DevExpress.XtraLayout.LayoutControl();
            this.lblAiColumnsDescription = new DevExpress.XtraEditors.LabelControl();
            this.btnSaveAiColumns = new DevExpress.XtraEditors.SimpleButton();
            this.memoEditAiColumns = new DevExpress.XtraEditors.MemoEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlItem5 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem6 = new DevExpress.XtraLayout.LayoutControlItem();
            this.saveAndApplyStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.newScriptButton = new DevExpress.XtraEditors.SimpleButton();
            this.applyScriptButton = new DevExpress.XtraEditors.SimpleButton();
            this.startScriptButton = new DevExpress.XtraEditors.SimpleButton();
            this.stopScriptButton = new DevExpress.XtraEditors.SimpleButton();
            this.lblServerStatusControl = new DevExpress.XtraEditors.LabelControl();
            this.lblServerStatusControlStatus = new DevExpress.XtraEditors.LabelControl();
            this.previewStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.lblUrl = new DevExpress.XtraEditors.LabelControl();
            this.txtPreviewFinalUrl = new DevExpress.XtraEditors.MemoEdit();
            this.txtTemplateUrl = new DevExpress.XtraEditors.MemoEdit();
            this.previewBrowserTitleStackPanel = new DevExpress.Utils.Layout.StackPanel();
            this.lblPreviewResponse = new DevExpress.XtraEditors.LabelControl();
            this.btnManageChromeProfile = new DevExpress.XtraEditors.SimpleButton();
            this.panelCefBrowser = new DevExpress.XtraEditors.PanelControl();
            this.progressBarControlDownloadRequiredFiles = new DevExpress.XtraEditors.ProgressBarControl();
            this.btnDownloadRequiredFiles = new DevExpress.XtraEditors.SimpleButton();
            this.PreviewControlGroup = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            this.lblSpace2 = new DevExpress.XtraEditors.LabelControl();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.behaviorManager1 = new DevExpress.Utils.Behaviors.BehaviorManager(this.components);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl3)).BeginInit();
            this.layoutControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.connectionTypeStackPanel)).BeginInit();
            this.connectionTypeStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.connectionTypeHorStackPanel)).BeginInit();
            this.connectionTypeHorStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkExternalDataEnabled.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioExternalDataGroups.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pythonInstallStackPanel)).BeginInit();
            this.pythonInstallStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.stepsAndStartStackPanel)).BeginInit();
            this.stepsAndStartStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.scriptStepsTabControl1)).BeginInit();
            this.scriptStepsTabControl1.SuspendLayout();
            this.scriptStepsTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.csvAllstackPanel)).BeginInit();
            this.csvAllstackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.browseCsvStackPanel)).BeginInit();
            this.browseCsvStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCsvFileName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.csvstackPanelMatchingScript)).BeginInit();
            this.csvstackPanelMatchingScript.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.stackPanel2)).BeginInit();
            this.stackPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.csvFieldsToUseForMatchListBoxControl)).BeginInit();
            this.scriptStepsTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.step2StackPanel)).BeginInit();
            this.step2StackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.sentItemsStackPanel)).BeginInit();
            this.sentItemsStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.stacklistFields)).BeginInit();
            this.stacklistFields.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkListUrlParams)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.stackCategoryFields)).BeginInit();
            this.stackCategoryFields.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkListUrlParamsCategories)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.sendFieldsDescSaveContStackPanel)).BeginInit();
            this.sendFieldsDescSaveContStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkSendDescriptionAndPictures.Properties)).BeginInit();
            this.scriptStepsTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.step3stackPanel)).BeginInit();
            this.step3stackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.matchingScriptEdit.Properties)).BeginInit();
            this.scriptStepsTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.step4stackPanel)).BeginInit();
            this.step4stackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.matchingTemplateEdit.Properties)).BeginInit();
            this.scriptAITabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtApiKey.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.aiPromptEdit.Properties)).BeginInit();
            this.scriptAIDisplayPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.memoEditAIDisplayTemplate.Properties)).BeginInit();
            this.tabAiColumns.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlAiColumns)).BeginInit();
            this.layoutControlAiColumns.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.memoEditAiColumns.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.saveAndApplyStackPanel)).BeginInit();
            this.saveAndApplyStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.previewStackPanel)).BeginInit();
            this.previewStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtPreviewFinalUrl.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTemplateUrl.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.previewBrowserTitleStackPanel)).BeginInit();
            this.previewBrowserTitleStackPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelCefBrowser)).BeginInit();
            this.panelCefBrowser.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.progressBarControlDownloadRequiredFiles.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PreviewControlGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.behaviorManager1)).BeginInit();
            this.SuspendLayout();
            // 
            // btnPreview
            // 
            this.btnPreview.Location = new System.Drawing.Point(156, 5);
            this.btnPreview.Name = "btnPreview";
            this.btnPreview.Size = new System.Drawing.Size(75, 23);
            this.btnPreview.StyleController = this.layoutControl3;
            this.btnPreview.TabIndex = 0;
            this.btnPreview.Text = "Preview";
            this.btnPreview.Click += new System.EventHandler(this.btnPreview_Click);
            // 
            // layoutControl3
            // 
            this.layoutControl3.Controls.Add(this.connectionTypeStackPanel);
            this.layoutControl3.Controls.Add(this.stepsAndStartStackPanel);
            this.layoutControl3.Controls.Add(this.previewStackPanel);
            this.layoutControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl3.Location = new System.Drawing.Point(0, 32);
            this.layoutControl3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.layoutControl3.Name = "layoutControl3";
            this.layoutControl3.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(-1510, 186, 650, 400);
            this.layoutControl3.Root = this.PreviewControlGroup;
            this.layoutControl3.Size = new System.Drawing.Size(712, 750);
            this.layoutControl3.TabIndex = 105;
            this.layoutControl3.Text = "layoutControl3";
            this.layoutControl3.Resize += new System.EventHandler(this.layoutControl3_Resize);
            // 
            // connectionTypeStackPanel
            // 
            this.connectionTypeStackPanel.AutoScroll = true;
            this.connectionTypeStackPanel.ContentImageAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.connectionTypeStackPanel.Controls.Add(this.connectionTypeHorStackPanel);
            this.connectionTypeStackPanel.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.connectionTypeStackPanel.Location = new System.Drawing.Point(2, 2);
            this.connectionTypeStackPanel.Margin = new System.Windows.Forms.Padding(0);
            this.connectionTypeStackPanel.Name = "connectionTypeStackPanel";
            this.connectionTypeStackPanel.Size = new System.Drawing.Size(708, 75);
            this.connectionTypeStackPanel.TabIndex = 98;
            this.connectionTypeStackPanel.UseSkinIndents = true;
            this.connectionTypeStackPanel.Paint += new System.Windows.Forms.PaintEventHandler(this.connectionTypeStackPanel_Paint);
            // 
            // connectionTypeHorStackPanel
            // 
            this.connectionTypeHorStackPanel.ContentImageAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.connectionTypeHorStackPanel.Controls.Add(this.chkExternalDataEnabled);
            this.connectionTypeHorStackPanel.Controls.Add(this.lblConnectionType);
            this.connectionTypeHorStackPanel.Controls.Add(this.radioExternalDataGroups);
            this.connectionTypeHorStackPanel.Controls.Add(this.pythonInstallStackPanel);
            this.connectionTypeHorStackPanel.Controls.Add(this.lblSpace);
            this.connectionTypeHorStackPanel.Location = new System.Drawing.Point(23, 10);
            this.connectionTypeHorStackPanel.Margin = new System.Windows.Forms.Padding(0);
            this.connectionTypeHorStackPanel.Name = "connectionTypeHorStackPanel";
            this.connectionTypeHorStackPanel.Size = new System.Drawing.Size(662, 51);
            this.connectionTypeHorStackPanel.TabIndex = 98;
            this.connectionTypeHorStackPanel.UseSkinIndents = true;
            // 
            // chkExternalDataEnabled
            // 
            this.chkExternalDataEnabled.Dock = System.Windows.Forms.DockStyle.Left;
            this.chkExternalDataEnabled.Location = new System.Drawing.Point(11, 13);
            this.chkExternalDataEnabled.Margin = new System.Windows.Forms.Padding(0);
            this.chkExternalDataEnabled.Name = "chkExternalDataEnabled";
            this.chkExternalDataEnabled.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkExternalDataEnabled.Properties.Appearance.Options.UseFont = true;
            this.chkExternalDataEnabled.Properties.AutoHeight = false;
            this.chkExternalDataEnabled.Properties.AutoWidth = true;
            this.chkExternalDataEnabled.Properties.Caption = "External Data Enabled";
            this.chkExternalDataEnabled.Size = new System.Drawing.Size(158, 23);
            this.chkExternalDataEnabled.StyleController = this.layoutControl3;
            this.chkExternalDataEnabled.TabIndex = 21;
            this.chkExternalDataEnabled.CheckedChanged += new System.EventHandler(this.chkExternalDataEnabled_CheckedChanged);
            // 
            // lblConnectionType
            // 
            this.lblConnectionType.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblConnectionType.Appearance.Options.UseFont = true;
            this.lblConnectionType.Location = new System.Drawing.Point(179, 18);
            this.lblConnectionType.Margin = new System.Windows.Forms.Padding(10, 3, 3, 3);
            this.lblConnectionType.Name = "lblConnectionType";
            this.lblConnectionType.Size = new System.Drawing.Size(29, 14);
            this.lblConnectionType.TabIndex = 24;
            this.lblConnectionType.Text = "Type";
            // 
            // radioExternalDataGroups
            // 
            this.radioExternalDataGroups.Location = new System.Drawing.Point(214, 13);
            this.radioExternalDataGroups.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.radioExternalDataGroups.MenuManager = this.ribbon;
            this.radioExternalDataGroups.Name = "radioExternalDataGroups";
            this.radioExternalDataGroups.Properties.Appearance.BackColor = System.Drawing.Color.White;
            this.radioExternalDataGroups.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radioExternalDataGroups.Properties.Appearance.Options.UseBackColor = true;
            this.radioExternalDataGroups.Properties.Appearance.Options.UseFont = true;
            this.radioExternalDataGroups.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.radioExternalDataGroups.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "Local Machine"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "External Endpoint"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "AI")});
            this.radioExternalDataGroups.Properties.ItemsLayout = DevExpress.XtraEditors.RadioGroupItemsLayout.Flow;
            this.radioExternalDataGroups.Properties.Padding = new System.Windows.Forms.Padding(0);
            this.radioExternalDataGroups.Size = new System.Drawing.Size(260, 24);
            this.radioExternalDataGroups.TabIndex = 23;
            this.radioExternalDataGroups.SelectedIndexChanged += new System.EventHandler(this.radioExternalDataGroups_SelectedIndexChanged);
            // 
            // ribbon
            // 
            this.ribbon.ExpandCollapseItem.Id = 0;
            this.ribbon.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbon.ExpandCollapseItem});
            this.ribbon.Location = new System.Drawing.Point(0, 0);
            this.ribbon.MaxItemId = 1;
            this.ribbon.Name = "ribbon";
            this.ribbon.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbon.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbon.Size = new System.Drawing.Size(712, 32);
            // 
            // pythonInstallStackPanel
            // 
            this.pythonInstallStackPanel.Controls.Add(this.installPythonLinkButton);
            this.pythonInstallStackPanel.Controls.Add(this.installPythonLibsButton);
            this.pythonInstallStackPanel.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.pythonInstallStackPanel.Location = new System.Drawing.Point(477, 3);
            this.pythonInstallStackPanel.Margin = new System.Windows.Forms.Padding(0);
            this.pythonInstallStackPanel.Name = "pythonInstallStackPanel";
            this.pythonInstallStackPanel.Padding = new System.Windows.Forms.Padding(0, 0, 0, 3);
            this.pythonInstallStackPanel.Size = new System.Drawing.Size(121, 45);
            this.pythonInstallStackPanel.TabIndex = 98;
            this.pythonInstallStackPanel.UseSkinIndents = true;
            // 
            // installPythonLinkButton
            // 
            this.installPythonLinkButton.Location = new System.Drawing.Point(0, 0);
            this.installPythonLinkButton.Margin = new System.Windows.Forms.Padding(0, 0, 0, 5);
            this.installPythonLinkButton.Name = "installPythonLinkButton";
            this.installPythonLinkButton.Padding = new System.Windows.Forms.Padding(0, 0, 0, 5);
            this.installPythonLinkButton.Size = new System.Drawing.Size(120, 20);
            this.installPythonLinkButton.TabIndex = 0;
            this.installPythonLinkButton.Text = "Install Python";
            this.installPythonLinkButton.Click += new System.EventHandler(this.installPythonLinkButton_Click);
            // 
            // installPythonLibsButton
            // 
            this.installPythonLibsButton.Location = new System.Drawing.Point(0, 25);
            this.installPythonLibsButton.Margin = new System.Windows.Forms.Padding(0);
            this.installPythonLibsButton.Name = "installPythonLibsButton";
            this.installPythonLibsButton.Size = new System.Drawing.Size(120, 20);
            this.installPythonLibsButton.TabIndex = 0;
            this.installPythonLibsButton.Text = "Setup Python libraries";
            this.installPythonLibsButton.Click += new System.EventHandler(this.installPythonLibsButton_Click);
            // 
            // lblSpace
            // 
            this.lblSpace.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblSpace.Appearance.Options.UseFont = true;
            this.lblSpace.Location = new System.Drawing.Point(600, 18);
            this.lblSpace.Name = "lblSpace";
            this.lblSpace.Size = new System.Drawing.Size(3, 13);
            this.lblSpace.TabIndex = 24;
            this.lblSpace.Text = " ";
            // 
            // stepsAndStartStackPanel
            // 
            this.stepsAndStartStackPanel.AutoScroll = true;
            this.stepsAndStartStackPanel.Controls.Add(this.scriptStepsTabControl1);
            this.stepsAndStartStackPanel.Controls.Add(this.saveAndApplyStackPanel);
            this.stepsAndStartStackPanel.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.stepsAndStartStackPanel.Location = new System.Drawing.Point(2, 81);
            this.stepsAndStartStackPanel.Margin = new System.Windows.Forms.Padding(0);
            this.stepsAndStartStackPanel.Name = "stepsAndStartStackPanel";
            this.stepsAndStartStackPanel.Size = new System.Drawing.Size(708, 357);
            this.stepsAndStartStackPanel.TabIndex = 100;
            this.stepsAndStartStackPanel.UseSkinIndents = true;
            // 
            // scriptStepsTabControl1
            // 
            this.scriptStepsTabControl1.Location = new System.Drawing.Point(14, 12);
            this.scriptStepsTabControl1.Name = "scriptStepsTabControl1";
            this.scriptStepsTabControl1.SelectedTabPage = this.scriptStepsTabPage1;
            this.scriptStepsTabControl1.Size = new System.Drawing.Size(680, 286);
            this.scriptStepsTabControl1.TabIndex = 7;
            this.scriptStepsTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.scriptStepsTabPage1,
            this.scriptStepsTabPage2,
            this.scriptStepsTabPage3,
            this.scriptStepsTabPage4,
            this.scriptAITabPage,
            this.scriptAIDisplayPage,
            this.tabAiColumns});
            this.scriptStepsTabControl1.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.scriptStepsTabControl1_SelectedPageChanged);
            // 
            // scriptStepsTabPage1
            // 
            this.scriptStepsTabPage1.Controls.Add(this.csvAllstackPanel);
            this.scriptStepsTabPage1.Name = "scriptStepsTabPage1";
            this.scriptStepsTabPage1.Size = new System.Drawing.Size(678, 261);
            this.scriptStepsTabPage1.Text = "Step 1";
            // 
            // csvAllstackPanel
            // 
            this.csvAllstackPanel.AutoScroll = true;
            this.csvAllstackPanel.Controls.Add(this.lblStep1Local);
            this.csvAllstackPanel.Controls.Add(this.browseCsvStackPanel);
            this.csvAllstackPanel.Controls.Add(this.csvstackPanelMatchingScript);
            this.csvAllstackPanel.Controls.Add(this.SaveAndContCsvButton);
            this.csvAllstackPanel.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.csvAllstackPanel.Location = new System.Drawing.Point(2, 6);
            this.csvAllstackPanel.Margin = new System.Windows.Forms.Padding(0);
            this.csvAllstackPanel.Name = "csvAllstackPanel";
            this.csvAllstackPanel.Size = new System.Drawing.Size(680, 236);
            this.csvAllstackPanel.TabIndex = 100;
            this.csvAllstackPanel.UseSkinIndents = true;
            this.csvAllstackPanel.Paint += new System.Windows.Forms.PaintEventHandler(this.csvAllstackPanel_Paint);
            // 
            // lblStep1Local
            // 
            this.lblStep1Local.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblStep1Local.Appearance.Options.UseFont = true;
            this.lblStep1Local.Location = new System.Drawing.Point(278, 10);
            this.lblStep1Local.Margin = new System.Windows.Forms.Padding(0);
            this.lblStep1Local.Name = "lblStep1Local";
            this.lblStep1Local.Size = new System.Drawing.Size(124, 14);
            this.lblStep1Local.TabIndex = 25;
            this.lblStep1Local.Text = "Step 1: Data Source";
            // 
            // browseCsvStackPanel
            // 
            this.browseCsvStackPanel.Controls.Add(this.lblCsvFile);
            this.browseCsvStackPanel.Controls.Add(this.txtCsvFileName);
            this.browseCsvStackPanel.Controls.Add(this.browseButtonCsv);
            this.browseCsvStackPanel.Location = new System.Drawing.Point(32, 26);
            this.browseCsvStackPanel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.browseCsvStackPanel.Name = "browseCsvStackPanel";
            this.browseCsvStackPanel.Size = new System.Drawing.Size(616, 30);
            this.browseCsvStackPanel.TabIndex = 98;
            this.browseCsvStackPanel.UseSkinIndents = true;
            // 
            // lblCsvFile
            // 
            this.lblCsvFile.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCsvFile.Appearance.Options.UseFont = true;
            this.lblCsvFile.Location = new System.Drawing.Point(13, 8);
            this.lblCsvFile.Name = "lblCsvFile";
            this.lblCsvFile.Size = new System.Drawing.Size(42, 13);
            this.lblCsvFile.TabIndex = 30;
            this.lblCsvFile.Text = "CSV File:";
            // 
            // txtCsvFileName
            // 
            this.txtCsvFileName.Location = new System.Drawing.Point(59, 5);
            this.txtCsvFileName.Name = "txtCsvFileName";
            this.txtCsvFileName.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtCsvFileName.Properties.Appearance.Options.UseFont = true;
            this.txtCsvFileName.Properties.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            this.txtCsvFileName.Size = new System.Drawing.Size(415, 20);
            this.txtCsvFileName.TabIndex = 29;
            // 
            // browseButtonCsv
            // 
            this.browseButtonCsv.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.browseButtonCsv.Appearance.BackColor = System.Drawing.Color.Blue;
            this.browseButtonCsv.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.browseButtonCsv.Appearance.Options.UseBackColor = true;
            this.browseButtonCsv.Appearance.Options.UseFont = true;
            this.browseButtonCsv.Location = new System.Drawing.Point(478, 2);
            this.browseButtonCsv.Name = "browseButtonCsv";
            this.browseButtonCsv.Size = new System.Drawing.Size(119, 25);
            this.browseButtonCsv.TabIndex = 31;
            this.browseButtonCsv.Text = "Browse";
            this.browseButtonCsv.Click += new System.EventHandler(this.browseButtonCsv_Click);
            // 
            // csvstackPanelMatchingScript
            // 
            this.csvstackPanelMatchingScript.ContentImageAlignment = System.Drawing.ContentAlignment.TopCenter;
            this.csvstackPanelMatchingScript.Controls.Add(this.stackPanel2);
            this.csvstackPanelMatchingScript.Controls.Add(this.csvFieldsToUseForMatchListBoxControl);
            this.csvstackPanelMatchingScript.Location = new System.Drawing.Point(24, 60);
            this.csvstackPanelMatchingScript.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.csvstackPanelMatchingScript.Name = "csvstackPanelMatchingScript";
            this.csvstackPanelMatchingScript.Size = new System.Drawing.Size(632, 64);
            this.csvstackPanelMatchingScript.TabIndex = 99;
            this.csvstackPanelMatchingScript.UseSkinIndents = true;
            // 
            // stackPanel2
            // 
            this.stackPanel2.Controls.Add(this.lblCsvFieldsToMatch);
            this.stackPanel2.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.stackPanel2.Location = new System.Drawing.Point(14, 2);
            this.stackPanel2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.stackPanel2.Name = "stackPanel2";
            this.stackPanel2.Size = new System.Drawing.Size(314, 59);
            this.stackPanel2.TabIndex = 34;
            this.stackPanel2.UseSkinIndents = true;
            // 
            // lblCsvFieldsToMatch
            // 
            this.lblCsvFieldsToMatch.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCsvFieldsToMatch.Appearance.Options.UseFont = true;
            this.lblCsvFieldsToMatch.Location = new System.Drawing.Point(42, 12);
            this.lblCsvFieldsToMatch.Name = "lblCsvFieldsToMatch";
            this.lblCsvFieldsToMatch.Size = new System.Drawing.Size(229, 13);
            this.lblCsvFieldsToMatch.TabIndex = 33;
            this.lblCsvFieldsToMatch.Text = "Select Fields from Csv to use in Matching Script:";
            this.lblCsvFieldsToMatch.Click += new System.EventHandler(this.lblCsvFieldsToMatch_Click);
            // 
            // csvFieldsToUseForMatchListBoxControl
            // 
            this.csvFieldsToUseForMatchListBoxControl.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.csvFieldsToUseForMatchListBoxControl.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.csvFieldsToUseForMatchListBoxControl.Appearance.Options.UseFont = true;
            this.csvFieldsToUseForMatchListBoxControl.Location = new System.Drawing.Point(333, 3);
            this.csvFieldsToUseForMatchListBoxControl.Name = "csvFieldsToUseForMatchListBoxControl";
            this.csvFieldsToUseForMatchListBoxControl.Size = new System.Drawing.Size(296, 57);
            this.csvFieldsToUseForMatchListBoxControl.TabIndex = 32;
            // 
            // SaveAndContCsvButton
            // 
            this.SaveAndContCsvButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.SaveAndContCsvButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.SaveAndContCsvButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.SaveAndContCsvButton.Appearance.Options.UseBackColor = true;
            this.SaveAndContCsvButton.Appearance.Options.UseFont = true;
            this.SaveAndContCsvButton.Appearance.Options.UseForeColor = true;
            this.SaveAndContCsvButton.Location = new System.Drawing.Point(247, 128);
            this.SaveAndContCsvButton.Name = "SaveAndContCsvButton";
            this.SaveAndContCsvButton.Size = new System.Drawing.Size(185, 22);
            this.SaveAndContCsvButton.TabIndex = 89;
            this.SaveAndContCsvButton.Text = "Save and Continue";
            this.SaveAndContCsvButton.Click += new System.EventHandler(this.SaveAndContCsvButton_Click);
            // 
            // scriptStepsTabPage2
            // 
            this.scriptStepsTabPage2.Controls.Add(this.step2StackPanel);
            this.scriptStepsTabPage2.Name = "scriptStepsTabPage2";
            this.scriptStepsTabPage2.Size = new System.Drawing.Size(678, 261);
            this.scriptStepsTabPage2.Text = "Step 2";
            // 
            // step2StackPanel
            // 
            this.step2StackPanel.AutoScroll = true;
            this.step2StackPanel.Controls.Add(this.lblStep2Local);
            this.step2StackPanel.Controls.Add(this.sentItemsStackPanel);
            this.step2StackPanel.Controls.Add(this.sendFieldsDescSaveContStackPanel);
            this.step2StackPanel.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.step2StackPanel.Location = new System.Drawing.Point(5, 12);
            this.step2StackPanel.Margin = new System.Windows.Forms.Padding(0);
            this.step2StackPanel.Name = "step2StackPanel";
            this.step2StackPanel.Size = new System.Drawing.Size(670, 220);
            this.step2StackPanel.TabIndex = 100;
            this.step2StackPanel.UseSkinIndents = true;
            // 
            // lblStep2Local
            // 
            this.lblStep2Local.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblStep2Local.Appearance.Options.UseFont = true;
            this.lblStep2Local.Location = new System.Drawing.Point(267, 12);
            this.lblStep2Local.Name = "lblStep2Local";
            this.lblStep2Local.Size = new System.Drawing.Size(135, 14);
            this.lblStep2Local.TabIndex = 35;
            this.lblStep2Local.Text = "Step 2: Fields to Send";
            // 
            // sentItemsStackPanel
            // 
            this.sentItemsStackPanel.ContentImageAlignment = System.Drawing.ContentAlignment.TopCenter;
            this.sentItemsStackPanel.Controls.Add(this.stacklistFields);
            this.sentItemsStackPanel.Controls.Add(this.stackCategoryFields);
            this.sentItemsStackPanel.Location = new System.Drawing.Point(93, 30);
            this.sentItemsStackPanel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.sentItemsStackPanel.Name = "sentItemsStackPanel";
            this.sentItemsStackPanel.Size = new System.Drawing.Size(483, 99);
            this.sentItemsStackPanel.TabIndex = 99;
            this.sentItemsStackPanel.UseSkinIndents = true;
            // 
            // stacklistFields
            // 
            this.stacklistFields.Controls.Add(this.lblStdListFields);
            this.stacklistFields.Controls.Add(this.chkListUrlParams);
            this.stacklistFields.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.stacklistFields.Location = new System.Drawing.Point(14, -8);
            this.stacklistFields.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.stacklistFields.Name = "stacklistFields";
            this.stacklistFields.Size = new System.Drawing.Size(230, 114);
            this.stacklistFields.TabIndex = 100;
            this.stacklistFields.UseSkinIndents = true;
            // 
            // lblStdListFields
            // 
            this.lblStdListFields.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblStdListFields.Appearance.Options.UseFont = true;
            this.lblStdListFields.Location = new System.Drawing.Point(59, 12);
            this.lblStdListFields.Name = "lblStdListFields";
            this.lblStdListFields.Size = new System.Drawing.Size(111, 13);
            this.lblStdListFields.TabIndex = 37;
            this.lblStdListFields.Text = "Standard Listing Fields:";
            // 
            // chkListUrlParams
            // 
            this.chkListUrlParams.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.chkListUrlParams.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkListUrlParams.Appearance.Options.UseFont = true;
            this.chkListUrlParams.Location = new System.Drawing.Point(1, 29);
            this.chkListUrlParams.Name = "chkListUrlParams";
            this.chkListUrlParams.Size = new System.Drawing.Size(228, 78);
            this.chkListUrlParams.TabIndex = 1;
            this.chkListUrlParams.ItemCheck += new DevExpress.XtraEditors.Controls.ItemCheckEventHandler(this.chkListUrlParams_ItemCheck);
            // 
            // stackCategoryFields
            // 
            this.stackCategoryFields.Controls.Add(this.lblCategoryItemSpecList);
            this.stackCategoryFields.Controls.Add(this.chkListUrlParamsCategories);
            this.stackCategoryFields.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.stackCategoryFields.Location = new System.Drawing.Point(250, -6);
            this.stackCategoryFields.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.stackCategoryFields.Name = "stackCategoryFields";
            this.stackCategoryFields.Size = new System.Drawing.Size(232, 110);
            this.stackCategoryFields.TabIndex = 100;
            this.stackCategoryFields.UseSkinIndents = true;
            // 
            // lblCategoryItemSpecList
            // 
            this.lblCategoryItemSpecList.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCategoryItemSpecList.Appearance.Options.UseFont = true;
            this.lblCategoryItemSpecList.Location = new System.Drawing.Point(57, 12);
            this.lblCategoryItemSpecList.Name = "lblCategoryItemSpecList";
            this.lblCategoryItemSpecList.Size = new System.Drawing.Size(118, 13);
            this.lblCategoryItemSpecList.TabIndex = 38;
            this.lblCategoryItemSpecList.Text = "Category Item Specifics:";
            // 
            // chkListUrlParamsCategories
            // 
            this.chkListUrlParamsCategories.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.chkListUrlParamsCategories.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkListUrlParamsCategories.Appearance.Options.UseFont = true;
            this.chkListUrlParamsCategories.Location = new System.Drawing.Point(5, 29);
            this.chkListUrlParamsCategories.Name = "chkListUrlParamsCategories";
            this.chkListUrlParamsCategories.Size = new System.Drawing.Size(222, 76);
            this.chkListUrlParamsCategories.TabIndex = 36;
            this.chkListUrlParamsCategories.ItemCheck += new DevExpress.XtraEditors.Controls.ItemCheckEventHandler(this.chkListUrlParamsCategories_ItemCheck);
            // 
            // sendFieldsDescSaveContStackPanel
            // 
            this.sendFieldsDescSaveContStackPanel.Controls.Add(this.chkSendDescriptionAndPictures);
            this.sendFieldsDescSaveContStackPanel.Controls.Add(this.SaveAndContFieldSenderButton);
            this.sendFieldsDescSaveContStackPanel.Location = new System.Drawing.Point(94, 133);
            this.sendFieldsDescSaveContStackPanel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.sendFieldsDescSaveContStackPanel.Name = "sendFieldsDescSaveContStackPanel";
            this.sendFieldsDescSaveContStackPanel.Size = new System.Drawing.Size(481, 39);
            this.sendFieldsDescSaveContStackPanel.TabIndex = 98;
            this.sendFieldsDescSaveContStackPanel.UseSkinIndents = true;
            // 
            // chkSendDescriptionAndPictures
            // 
            this.chkSendDescriptionAndPictures.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.chkSendDescriptionAndPictures.Location = new System.Drawing.Point(15, 11);
            this.chkSendDescriptionAndPictures.MenuManager = this.ribbon;
            this.chkSendDescriptionAndPictures.Name = "chkSendDescriptionAndPictures";
            this.chkSendDescriptionAndPictures.Properties.Caption = "Send description and pictures";
            this.chkSendDescriptionAndPictures.Size = new System.Drawing.Size(200, 20);
            this.chkSendDescriptionAndPictures.TabIndex = 19;
            this.chkSendDescriptionAndPictures.CheckedChanged += new System.EventHandler(this.chkSendDescriptionAndPictures_CheckedChanged);
            // 
            // SaveAndContFieldSenderButton
            // 
            this.SaveAndContFieldSenderButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.SaveAndContFieldSenderButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.SaveAndContFieldSenderButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.SaveAndContFieldSenderButton.Appearance.Options.UseBackColor = true;
            this.SaveAndContFieldSenderButton.Appearance.Options.UseFont = true;
            this.SaveAndContFieldSenderButton.Appearance.Options.UseForeColor = true;
            this.SaveAndContFieldSenderButton.Location = new System.Drawing.Point(221, 9);
            this.SaveAndContFieldSenderButton.Name = "SaveAndContFieldSenderButton";
            this.SaveAndContFieldSenderButton.Size = new System.Drawing.Size(189, 20);
            this.SaveAndContFieldSenderButton.TabIndex = 90;
            this.SaveAndContFieldSenderButton.Text = "Save and Continue";
            this.SaveAndContFieldSenderButton.Click += new System.EventHandler(this.SaveAndContFieldSenderButton_Click);
            // 
            // scriptStepsTabPage3
            // 
            this.scriptStepsTabPage3.Controls.Add(this.step3stackPanel);
            this.scriptStepsTabPage3.Name = "scriptStepsTabPage3";
            this.scriptStepsTabPage3.Size = new System.Drawing.Size(678, 261);
            this.scriptStepsTabPage3.Text = "Step 3";
            // 
            // step3stackPanel
            // 
            this.step3stackPanel.AutoScroll = true;
            this.step3stackPanel.Controls.Add(this.lblMatchingScript);
            this.step3stackPanel.Controls.Add(this.matchingScriptEdit);
            this.step3stackPanel.Controls.Add(this.SaveAndContMatchScriptButton);
            this.step3stackPanel.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.step3stackPanel.Location = new System.Drawing.Point(5, 12);
            this.step3stackPanel.Margin = new System.Windows.Forms.Padding(0);
            this.step3stackPanel.Name = "step3stackPanel";
            this.step3stackPanel.Size = new System.Drawing.Size(670, 236);
            this.step3stackPanel.TabIndex = 100;
            this.step3stackPanel.UseSkinIndents = true;
            // 
            // lblMatchingScript
            // 
            this.lblMatchingScript.Appearance.Font = new System.Drawing.Font("Tahoma", 8F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblMatchingScript.Appearance.Options.UseFont = true;
            this.lblMatchingScript.Location = new System.Drawing.Point(270, 12);
            this.lblMatchingScript.Name = "lblMatchingScript";
            this.lblMatchingScript.Size = new System.Drawing.Size(130, 13);
            this.lblMatchingScript.TabIndex = 92;
            this.lblMatchingScript.Text = "Step 3: Matching Script";
            this.lblMatchingScript.Click += new System.EventHandler(this.lblMatchingScript_Click);
            // 
            // matchingScriptEdit
            // 
            this.matchingScriptEdit.Location = new System.Drawing.Point(26, 29);
            this.matchingScriptEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.matchingScriptEdit.MenuManager = this.ribbon;
            this.matchingScriptEdit.Name = "matchingScriptEdit";
            this.matchingScriptEdit.Size = new System.Drawing.Size(618, 177);
            this.matchingScriptEdit.TabIndex = 91;
            // 
            // SaveAndContMatchScriptButton
            // 
            this.SaveAndContMatchScriptButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.SaveAndContMatchScriptButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.SaveAndContMatchScriptButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.SaveAndContMatchScriptButton.Appearance.Options.UseBackColor = true;
            this.SaveAndContMatchScriptButton.Appearance.Options.UseFont = true;
            this.SaveAndContMatchScriptButton.Appearance.Options.UseForeColor = true;
            this.SaveAndContMatchScriptButton.Location = new System.Drawing.Point(251, 210);
            this.SaveAndContMatchScriptButton.Name = "SaveAndContMatchScriptButton";
            this.SaveAndContMatchScriptButton.Size = new System.Drawing.Size(168, 21);
            this.SaveAndContMatchScriptButton.TabIndex = 93;
            this.SaveAndContMatchScriptButton.Text = "Save and Continue";
            this.SaveAndContMatchScriptButton.Click += new System.EventHandler(this.SaveAndContMatchScriptButton_Click);
            // 
            // scriptStepsTabPage4
            // 
            this.scriptStepsTabPage4.Controls.Add(this.step4stackPanel);
            this.scriptStepsTabPage4.Name = "scriptStepsTabPage4";
            this.scriptStepsTabPage4.Size = new System.Drawing.Size(678, 261);
            this.scriptStepsTabPage4.Text = "Step 4";
            // 
            // step4stackPanel
            // 
            this.step4stackPanel.AutoScroll = true;
            this.step4stackPanel.Controls.Add(this.lblTemplateScript);
            this.step4stackPanel.Controls.Add(this.matchingTemplateEdit);
            this.step4stackPanel.Controls.Add(this.SaveAndContTemplateButton);
            this.step4stackPanel.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.step4stackPanel.Location = new System.Drawing.Point(5, 12);
            this.step4stackPanel.Margin = new System.Windows.Forms.Padding(0);
            this.step4stackPanel.Name = "step4stackPanel";
            this.step4stackPanel.Size = new System.Drawing.Size(670, 236);
            this.step4stackPanel.TabIndex = 100;
            this.step4stackPanel.UseSkinIndents = true;
            // 
            // lblTemplateScript
            // 
            this.lblTemplateScript.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTemplateScript.Appearance.Options.UseFont = true;
            this.lblTemplateScript.Location = new System.Drawing.Point(265, 12);
            this.lblTemplateScript.Name = "lblTemplateScript";
            this.lblTemplateScript.Size = new System.Drawing.Size(140, 13);
            this.lblTemplateScript.TabIndex = 92;
            this.lblTemplateScript.Text = "Step 4: Display Template";
            // 
            // matchingTemplateEdit
            // 
            this.matchingTemplateEdit.Location = new System.Drawing.Point(22, 29);
            this.matchingTemplateEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.matchingTemplateEdit.MenuManager = this.ribbon;
            this.matchingTemplateEdit.Name = "matchingTemplateEdit";
            this.matchingTemplateEdit.Size = new System.Drawing.Size(625, 177);
            this.matchingTemplateEdit.TabIndex = 91;
            // 
            // SaveAndContTemplateButton
            // 
            this.SaveAndContTemplateButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.SaveAndContTemplateButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.SaveAndContTemplateButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.SaveAndContTemplateButton.Appearance.Options.UseBackColor = true;
            this.SaveAndContTemplateButton.Appearance.Options.UseFont = true;
            this.SaveAndContTemplateButton.Appearance.Options.UseForeColor = true;
            this.SaveAndContTemplateButton.Location = new System.Drawing.Point(256, 210);
            this.SaveAndContTemplateButton.Name = "SaveAndContTemplateButton";
            this.SaveAndContTemplateButton.Size = new System.Drawing.Size(158, 20);
            this.SaveAndContTemplateButton.TabIndex = 93;
            this.SaveAndContTemplateButton.Text = "Save and Continue";
            this.SaveAndContTemplateButton.Click += new System.EventHandler(this.SaveAndContTemplateButton_Click);
            // 
            // scriptAITabPage
            // 
            this.scriptAITabPage.Controls.Add(this.saveAIConfigurationButton);
            this.scriptAITabPage.Controls.Add(this.modelComboBox);
            this.scriptAITabPage.Controls.Add(this.aiProviderComboBox);
            this.scriptAITabPage.Controls.Add(this.lblModel);
            this.scriptAITabPage.Controls.Add(this.txtApiKey);
            this.scriptAITabPage.Controls.Add(this.aiPromptEdit);
            this.scriptAITabPage.Controls.Add(this.lblAiPrompt);
            this.scriptAITabPage.Controls.Add(this.lblApiKey);
            this.scriptAITabPage.Controls.Add(this.lblAIProviderDropdown);
            this.scriptAITabPage.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.scriptAITabPage.Name = "scriptAITabPage";
            this.scriptAITabPage.Size = new System.Drawing.Size(678, 261);
            this.scriptAITabPage.Text = "AI";
            // 
            // saveAIConfigurationButton
            // 
            this.saveAIConfigurationButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.saveAIConfigurationButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.saveAIConfigurationButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.saveAIConfigurationButton.Appearance.Options.UseBackColor = true;
            this.saveAIConfigurationButton.Appearance.Options.UseFont = true;
            this.saveAIConfigurationButton.Appearance.Options.UseForeColor = true;
            this.saveAIConfigurationButton.Location = new System.Drawing.Point(278, 236);
            this.saveAIConfigurationButton.Name = "saveAIConfigurationButton";
            this.saveAIConfigurationButton.Size = new System.Drawing.Size(126, 19);
            this.saveAIConfigurationButton.TabIndex = 106;
            this.saveAIConfigurationButton.Text = "Save Configuration";
            this.saveAIConfigurationButton.Click += new System.EventHandler(this.saveAIConfigurationButton_Click);
            // 
            // modelComboBox
            // 
            this.modelComboBox.FormattingEnabled = true;
            this.modelComboBox.Location = new System.Drawing.Point(66, 212);
            this.modelComboBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.modelComboBox.Name = "modelComboBox";
            this.modelComboBox.Size = new System.Drawing.Size(584, 21);
            this.modelComboBox.TabIndex = 105;
            this.modelComboBox.SelectedIndexChanged += new System.EventHandler(this.modelComboBox_SelectedIndexChanged);
            // 
            // aiProviderComboBox
            // 
            this.aiProviderComboBox.FormattingEnabled = true;
            this.aiProviderComboBox.Location = new System.Drawing.Point(93, 3);
            this.aiProviderComboBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.aiProviderComboBox.Name = "aiProviderComboBox";
            this.aiProviderComboBox.Size = new System.Drawing.Size(557, 21);
            this.aiProviderComboBox.TabIndex = 104;
            this.aiProviderComboBox.SelectedIndexChanged += new System.EventHandler(this.aiProviderComboBox_SelectedIndexChanged);
            // 
            // lblModel
            // 
            this.lblModel.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblModel.Appearance.Options.UseFont = true;
            this.lblModel.Location = new System.Drawing.Point(26, 214);
            this.lblModel.Name = "lblModel";
            this.lblModel.Size = new System.Drawing.Size(32, 13);
            this.lblModel.TabIndex = 103;
            this.lblModel.Text = "Model:";
            // 
            // txtApiKey
            // 
            this.txtApiKey.Location = new System.Drawing.Point(93, 26);
            this.txtApiKey.Name = "txtApiKey";
            this.txtApiKey.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtApiKey.Properties.Appearance.Options.UseFont = true;
            this.txtApiKey.Properties.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            this.txtApiKey.Size = new System.Drawing.Size(556, 20);
            this.txtApiKey.TabIndex = 101;
            // 
            // aiPromptEdit
            // 
            this.aiPromptEdit.Location = new System.Drawing.Point(25, 64);
            this.aiPromptEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.aiPromptEdit.Name = "aiPromptEdit";
            this.aiPromptEdit.Size = new System.Drawing.Size(625, 145);
            this.aiPromptEdit.TabIndex = 100;
            // 
            // lblAiPrompt
            // 
            this.lblAiPrompt.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAiPrompt.Appearance.Options.UseFont = true;
            this.lblAiPrompt.Location = new System.Drawing.Point(28, 48);
            this.lblAiPrompt.Name = "lblAiPrompt";
            this.lblAiPrompt.Size = new System.Drawing.Size(52, 13);
            this.lblAiPrompt.TabIndex = 99;
            this.lblAiPrompt.Text = "AI Prompt:";
            // 
            // lblApiKey
            // 
            this.lblApiKey.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblApiKey.Appearance.Options.UseFont = true;
            this.lblApiKey.Location = new System.Drawing.Point(27, 29);
            this.lblApiKey.Name = "lblApiKey";
            this.lblApiKey.Size = new System.Drawing.Size(42, 13);
            this.lblApiKey.TabIndex = 98;
            this.lblApiKey.Text = "API Key:";
            // 
            // lblAIProviderDropdown
            // 
            this.lblAIProviderDropdown.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAIProviderDropdown.Appearance.Options.UseFont = true;
            this.lblAIProviderDropdown.Location = new System.Drawing.Point(26, 6);
            this.lblAIProviderDropdown.Name = "lblAIProviderDropdown";
            this.lblAIProviderDropdown.Size = new System.Drawing.Size(58, 13);
            this.lblAIProviderDropdown.TabIndex = 97;
            this.lblAIProviderDropdown.Text = "AI Provider:";
            // 
            // scriptAIDisplayPage
            // 
            this.scriptAIDisplayPage.Controls.Add(this.saveAIConfigurationDisplayButton);
            this.scriptAIDisplayPage.Controls.Add(this.memoEditAIDisplayTemplate);
            this.scriptAIDisplayPage.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.scriptAIDisplayPage.Name = "scriptAIDisplayPage";
            this.scriptAIDisplayPage.Size = new System.Drawing.Size(678, 261);
            this.scriptAIDisplayPage.Text = "AI Display Template";
            // 
            // saveAIConfigurationDisplayButton
            // 
            this.saveAIConfigurationDisplayButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.saveAIConfigurationDisplayButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.saveAIConfigurationDisplayButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.saveAIConfigurationDisplayButton.Appearance.Options.UseBackColor = true;
            this.saveAIConfigurationDisplayButton.Appearance.Options.UseFont = true;
            this.saveAIConfigurationDisplayButton.Appearance.Options.UseForeColor = true;
            this.saveAIConfigurationDisplayButton.Location = new System.Drawing.Point(278, 236);
            this.saveAIConfigurationDisplayButton.Name = "saveAIConfigurationDisplayButton";
            this.saveAIConfigurationDisplayButton.Size = new System.Drawing.Size(126, 19);
            this.saveAIConfigurationDisplayButton.TabIndex = 107;
            this.saveAIConfigurationDisplayButton.Text = "Save Configuration";
            this.saveAIConfigurationDisplayButton.Click += new System.EventHandler(this.saveAIConfigurationDisplayButton_Click);
            // 
            // memoEditAIDisplayTemplate
            // 
            this.memoEditAIDisplayTemplate.Location = new System.Drawing.Point(15, 13);
            this.memoEditAIDisplayTemplate.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.memoEditAIDisplayTemplate.MenuManager = this.ribbon;
            this.memoEditAIDisplayTemplate.Name = "memoEditAIDisplayTemplate";
            this.memoEditAIDisplayTemplate.Size = new System.Drawing.Size(641, 219);
            this.memoEditAIDisplayTemplate.TabIndex = 92;
            // 
            // tabAiColumns
            // 
            this.tabAiColumns.Controls.Add(this.layoutControlAiColumns);
            this.tabAiColumns.Name = "tabAiColumns";
            this.tabAiColumns.Size = new System.Drawing.Size(678, 261);
            this.tabAiColumns.Text = "Ai Columns";
            // 
            // layoutControlAiColumns
            // 
            this.layoutControlAiColumns.Controls.Add(this.lblAiColumnsDescription);
            this.layoutControlAiColumns.Controls.Add(this.btnSaveAiColumns);
            this.layoutControlAiColumns.Controls.Add(this.memoEditAiColumns);
            this.layoutControlAiColumns.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControlAiColumns.Location = new System.Drawing.Point(0, 0);
            this.layoutControlAiColumns.Name = "layoutControlAiColumns";
            this.layoutControlAiColumns.Root = this.Root;
            this.layoutControlAiColumns.Size = new System.Drawing.Size(678, 261);
            this.layoutControlAiColumns.TabIndex = 110;
            this.layoutControlAiColumns.Text = "layoutControlAiColumns";
            // 
            // lblAiColumnsDescription
            // 
            this.lblAiColumnsDescription.Location = new System.Drawing.Point(177, 12);
            this.lblAiColumnsDescription.Name = "lblAiColumnsDescription";
            this.lblAiColumnsDescription.Size = new System.Drawing.Size(299, 13);
            this.lblAiColumnsDescription.StyleController = this.layoutControlAiColumns;
            this.lblAiColumnsDescription.TabIndex = 109;
            this.lblAiColumnsDescription.Text = "Provide a list of AiColumns for Results Grid each on a new line.";
            // 
            // btnSaveAiColumns
            // 
            this.btnSaveAiColumns.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.btnSaveAiColumns.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSaveAiColumns.Appearance.ForeColor = System.Drawing.Color.Black;
            this.btnSaveAiColumns.Appearance.Options.UseBackColor = true;
            this.btnSaveAiColumns.Appearance.Options.UseFont = true;
            this.btnSaveAiColumns.Appearance.Options.UseForeColor = true;
            this.btnSaveAiColumns.Location = new System.Drawing.Point(177, 227);
            this.btnSaveAiColumns.Name = "btnSaveAiColumns";
            this.btnSaveAiColumns.Size = new System.Drawing.Size(299, 22);
            this.btnSaveAiColumns.StyleController = this.layoutControlAiColumns;
            this.btnSaveAiColumns.TabIndex = 108;
            this.btnSaveAiColumns.Text = "Save Configuration";
            this.btnSaveAiColumns.Click += new System.EventHandler(this.btnSaveAiColumns_Click);
            // 
            // memoEditAiColumns
            // 
            this.memoEditAiColumns.Location = new System.Drawing.Point(177, 29);
            this.memoEditAiColumns.MenuManager = this.ribbon;
            this.memoEditAiColumns.Name = "memoEditAiColumns";
            this.memoEditAiColumns.Size = new System.Drawing.Size(299, 194);
            this.memoEditAiColumns.StyleController = this.layoutControlAiColumns;
            this.memoEditAiColumns.TabIndex = 0;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem3,
            this.emptySpaceItem2,
            this.emptySpaceItem3,
            this.layoutControlItem5,
            this.layoutControlItem6});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(678, 261);
            this.Root.TextVisible = false;
            // 
            // layoutControlItem3
            // 
            this.layoutControlItem3.Control = this.memoEditAiColumns;
            this.layoutControlItem3.Location = new System.Drawing.Point(165, 17);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(303, 198);
            this.layoutControlItem3.TextVisible = false;
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.Location = new System.Drawing.Point(0, 0);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(165, 241);
            // 
            // emptySpaceItem3
            // 
            this.emptySpaceItem3.Location = new System.Drawing.Point(468, 0);
            this.emptySpaceItem3.Name = "emptySpaceItem3";
            this.emptySpaceItem3.Size = new System.Drawing.Size(190, 241);
            // 
            // layoutControlItem5
            // 
            this.layoutControlItem5.Control = this.btnSaveAiColumns;
            this.layoutControlItem5.Location = new System.Drawing.Point(165, 215);
            this.layoutControlItem5.Name = "layoutControlItem5";
            this.layoutControlItem5.Size = new System.Drawing.Size(303, 26);
            this.layoutControlItem5.TextVisible = false;
            // 
            // layoutControlItem6
            // 
            this.layoutControlItem6.Control = this.lblAiColumnsDescription;
            this.layoutControlItem6.Location = new System.Drawing.Point(165, 0);
            this.layoutControlItem6.Name = "layoutControlItem6";
            this.layoutControlItem6.Size = new System.Drawing.Size(303, 17);
            this.layoutControlItem6.TextVisible = false;
            // 
            // saveAndApplyStackPanel
            // 
            this.saveAndApplyStackPanel.Controls.Add(this.newScriptButton);
            this.saveAndApplyStackPanel.Controls.Add(this.applyScriptButton);
            this.saveAndApplyStackPanel.Controls.Add(this.startScriptButton);
            this.saveAndApplyStackPanel.Controls.Add(this.stopScriptButton);
            this.saveAndApplyStackPanel.Controls.Add(this.lblServerStatusControl);
            this.saveAndApplyStackPanel.Controls.Add(this.lblServerStatusControlStatus);
            this.saveAndApplyStackPanel.Location = new System.Drawing.Point(78, 376);
            this.saveAndApplyStackPanel.Margin = new System.Windows.Forms.Padding(4);
            this.saveAndApplyStackPanel.Name = "saveAndApplyStackPanel";
            this.saveAndApplyStackPanel.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.saveAndApplyStackPanel.Size = new System.Drawing.Size(557, 35);
            this.saveAndApplyStackPanel.TabIndex = 97;
            this.saveAndApplyStackPanel.UseSkinIndents = true;
            // 
            // newScriptButton
            // 
            this.newScriptButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.newScriptButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.newScriptButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.newScriptButton.Appearance.Options.UseBackColor = true;
            this.newScriptButton.Appearance.Options.UseFont = true;
            this.newScriptButton.Appearance.Options.UseForeColor = true;
            this.newScriptButton.Location = new System.Drawing.Point(13, 8);
            this.newScriptButton.Name = "newScriptButton";
            this.newScriptButton.Size = new System.Drawing.Size(126, 19);
            this.newScriptButton.TabIndex = 95;
            this.newScriptButton.Text = "Reset to Default";
            this.newScriptButton.Click += new System.EventHandler(this.newScriptButton_Click);
            // 
            // applyScriptButton
            // 
            this.applyScriptButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.applyScriptButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.applyScriptButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.applyScriptButton.Appearance.Options.UseBackColor = true;
            this.applyScriptButton.Appearance.Options.UseFont = true;
            this.applyScriptButton.Appearance.Options.UseForeColor = true;
            this.applyScriptButton.Location = new System.Drawing.Point(143, 8);
            this.applyScriptButton.Name = "applyScriptButton";
            this.applyScriptButton.Size = new System.Drawing.Size(126, 19);
            this.applyScriptButton.TabIndex = 95;
            this.applyScriptButton.Text = "Save All";
            this.applyScriptButton.Click += new System.EventHandler(this.applyScriptButton_Click);
            // 
            // startScriptButton
            // 
            this.startScriptButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.startScriptButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.startScriptButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.startScriptButton.Appearance.Options.UseBackColor = true;
            this.startScriptButton.Appearance.Options.UseFont = true;
            this.startScriptButton.Appearance.Options.UseForeColor = true;
            this.startScriptButton.Location = new System.Drawing.Point(273, 7);
            this.startScriptButton.Name = "startScriptButton";
            this.startScriptButton.Size = new System.Drawing.Size(126, 21);
            this.startScriptButton.TabIndex = 96;
            this.startScriptButton.Text = "Start Script";
            this.startScriptButton.Click += new System.EventHandler(this.startScriptButton_Click);
            // 
            // stopScriptButton
            // 
            this.stopScriptButton.Appearance.BackColor = System.Drawing.Color.LimeGreen;
            this.stopScriptButton.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.stopScriptButton.Appearance.ForeColor = System.Drawing.Color.Black;
            this.stopScriptButton.Appearance.Options.UseBackColor = true;
            this.stopScriptButton.Appearance.Options.UseFont = true;
            this.stopScriptButton.Appearance.Options.UseForeColor = true;
            this.stopScriptButton.Location = new System.Drawing.Point(403, 7);
            this.stopScriptButton.Name = "stopScriptButton";
            this.stopScriptButton.Size = new System.Drawing.Size(126, 21);
            this.stopScriptButton.TabIndex = 98;
            this.stopScriptButton.Text = "Stop Script";
            this.stopScriptButton.Click += new System.EventHandler(this.stopScriptButton_Click);
            // 
            // lblServerStatusControl
            // 
            this.lblServerStatusControl.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblServerStatusControl.Appearance.Options.UseFont = true;
            this.lblServerStatusControl.Margin = new System.Windows.Forms.Padding(4);
            this.lblServerStatusControl.Name = "lblServerStatusControl";
            this.lblServerStatusControl.Size = new System.Drawing.Size(72, 17);
            this.lblServerStatusControl.TabIndex = 97;
            this.lblServerStatusControl.Text = "Server Status:";
            // 
            // lblServerStatusControlStatus
            // 
            this.lblServerStatusControlStatus.Appearance.Font = new System.Drawing.Font("Tahoma", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblServerStatusControlStatus.Appearance.Options.UseFont = true;
            this.lblServerStatusControlStatus.Margin = new System.Windows.Forms.Padding(4);
            this.lblServerStatusControlStatus.ForeColor = System.Drawing.Color.Red;
            this.lblServerStatusControlStatus.Name = "lblServerStatusControlStatus";
            this.lblServerStatusControlStatus.Size = new System.Drawing.Size(60, 17);
            this.lblServerStatusControlStatus.TabIndex = 97;
            this.lblServerStatusControlStatus.Text = "Stopped";
            // 
            // previewStackPanel
            // 
            this.previewStackPanel.AutoScroll = true;
            this.previewStackPanel.Controls.Add(this.lblUrl);
            this.previewStackPanel.Controls.Add(this.txtPreviewFinalUrl);
            this.previewStackPanel.Controls.Add(this.txtTemplateUrl);
            this.previewStackPanel.Controls.Add(this.previewBrowserTitleStackPanel);
            this.previewStackPanel.Controls.Add(this.panelCefBrowser);
            this.previewStackPanel.LayoutDirection = DevExpress.Utils.Layout.StackPanelLayoutDirection.TopDown;
            this.previewStackPanel.Location = new System.Drawing.Point(2, 442);
            this.previewStackPanel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.previewStackPanel.Name = "previewStackPanel";
            this.previewStackPanel.Size = new System.Drawing.Size(708, 306);
            this.previewStackPanel.TabIndex = 100;
            this.previewStackPanel.UseSkinIndents = true;
            // 
            // lblUrl
            // 
            this.lblUrl.Location = new System.Drawing.Point(322, 12);
            this.lblUrl.Name = "lblUrl";
            this.lblUrl.Size = new System.Drawing.Size(64, 13);
            this.lblUrl.StyleController = this.layoutControl3;
            this.lblUrl.TabIndex = 6;
            this.lblUrl.Text = "Endpoint URL";
            // 
            // txtPreviewFinalUrl
            // 
            this.txtPreviewFinalUrl.Location = new System.Drawing.Point(29, 29);
            this.txtPreviewFinalUrl.MenuManager = this.ribbon;
            this.txtPreviewFinalUrl.Name = "txtPreviewFinalUrl";
            this.txtPreviewFinalUrl.Properties.ReadOnly = true;
            this.txtPreviewFinalUrl.Size = new System.Drawing.Size(650, 19);
            this.txtPreviewFinalUrl.StyleController = this.layoutControl3;
            this.txtPreviewFinalUrl.TabIndex = 11;
            // 
            // txtTemplateUrl
            // 
            this.txtTemplateUrl.EditValue = "https://www.amazon.com/s?k={Title}";
            this.txtTemplateUrl.Location = new System.Drawing.Point(29, 52);
            this.txtTemplateUrl.MenuManager = this.ribbon;
            this.txtTemplateUrl.Name = "txtTemplateUrl";
            this.txtTemplateUrl.Properties.EditValueChangedDelay = 100;
            this.txtTemplateUrl.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Buffered;
            this.txtTemplateUrl.Properties.NullValuePrompt = "http://mywebsite.com/myfolder/myscript.php?UPC={UPC}&Title={Title}";
            this.txtTemplateUrl.Size = new System.Drawing.Size(650, 42);
            this.txtTemplateUrl.StyleController = this.layoutControl3;
            this.txtTemplateUrl.TabIndex = 2;
            this.txtTemplateUrl.TabStop = false;
            this.txtTemplateUrl.EditValueChanged += new System.EventHandler(this.txtTemplateUrl_EditValueChanged);
            // 
            // previewBrowserTitleStackPanel
            // 
            this.previewBrowserTitleStackPanel.Controls.Add(this.lblPreviewResponse);
            this.previewBrowserTitleStackPanel.Controls.Add(this.btnPreview);
            this.previewBrowserTitleStackPanel.Controls.Add(this.btnManageChromeProfile);
            this.previewBrowserTitleStackPanel.Location = new System.Drawing.Point(108, 98);
            this.previewBrowserTitleStackPanel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.previewBrowserTitleStackPanel.Name = "previewBrowserTitleStackPanel";
            this.previewBrowserTitleStackPanel.Size = new System.Drawing.Size(492, 33);
            this.previewBrowserTitleStackPanel.TabIndex = 100;
            this.previewBrowserTitleStackPanel.UseSkinIndents = true;
            // 
            // lblPreviewResponse
            // 
            this.lblPreviewResponse.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblPreviewResponse.Appearance.Options.UseFont = true;
            this.lblPreviewResponse.Location = new System.Drawing.Point(13, 9);
            this.lblPreviewResponse.Name = "lblPreviewResponse";
            this.lblPreviewResponse.Size = new System.Drawing.Size(139, 14);
            this.lblPreviewResponse.StyleController = this.layoutControl3;
            this.lblPreviewResponse.TabIndex = 9;
            this.lblPreviewResponse.Text = "External Panel Preview";
            // 
            // btnManageChromeProfile
            // 
            this.btnManageChromeProfile.Location = new System.Drawing.Point(235, 2);
            this.btnManageChromeProfile.Name = "btnManageChromeProfile";
            this.btnManageChromeProfile.Size = new System.Drawing.Size(214, 29);
            this.btnManageChromeProfile.StyleController = this.layoutControl3;
            this.btnManageChromeProfile.TabIndex = 17;
            this.btnManageChromeProfile.Text = "Manage Chrome Profile";
            this.btnManageChromeProfile.Visible = false;
            this.btnManageChromeProfile.Click += new System.EventHandler(this.btnManageChromeProfile_Click);
            // 
            // panelCefBrowser
            // 
            this.panelCefBrowser.Controls.Add(this.progressBarControlDownloadRequiredFiles);
            this.panelCefBrowser.Controls.Add(this.btnDownloadRequiredFiles);
            this.panelCefBrowser.Location = new System.Drawing.Point(29, 135);
            this.panelCefBrowser.Name = "panelCefBrowser";
            this.panelCefBrowser.Size = new System.Drawing.Size(650, 164);
            this.panelCefBrowser.TabIndex = 15;
            // 
            // progressBarControlDownloadRequiredFiles
            // 
            this.progressBarControlDownloadRequiredFiles.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.progressBarControlDownloadRequiredFiles.Location = new System.Drawing.Point(5, 34);
            this.progressBarControlDownloadRequiredFiles.Name = "progressBarControlDownloadRequiredFiles";
            this.progressBarControlDownloadRequiredFiles.Size = new System.Drawing.Size(638, 18);
            this.progressBarControlDownloadRequiredFiles.TabIndex = 1;
            this.progressBarControlDownloadRequiredFiles.Visible = false;
            // 
            // btnDownloadRequiredFiles
            // 
            this.btnDownloadRequiredFiles.Location = new System.Drawing.Point(5, 5);
            this.btnDownloadRequiredFiles.Name = "btnDownloadRequiredFiles";
            this.btnDownloadRequiredFiles.Size = new System.Drawing.Size(131, 23);
            this.btnDownloadRequiredFiles.TabIndex = 0;
            this.btnDownloadRequiredFiles.Text = "Download Chrome";
            this.btnDownloadRequiredFiles.Visible = false;
            this.btnDownloadRequiredFiles.Click += new System.EventHandler(this.btnDownloadFiles_Click);
            // 
            // PreviewControlGroup
            // 
            this.PreviewControlGroup.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.PreviewControlGroup.GroupBordersVisible = false;
            this.PreviewControlGroup.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem2,
            this.layoutControlItem1,
            this.layoutControlItem4});
            this.PreviewControlGroup.Name = "Root";
            this.PreviewControlGroup.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.PreviewControlGroup.Size = new System.Drawing.Size(712, 750);
            this.PreviewControlGroup.TextVisible = false;
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.Control = this.connectionTypeStackPanel;
            this.layoutControlItem2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(712, 79);
            this.layoutControlItem2.TextVisible = false;
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.stepsAndStartStackPanel;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 79);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(712, 361);
            this.layoutControlItem1.TextVisible = false;
            // 
            // layoutControlItem4
            // 
            this.layoutControlItem4.Control = this.previewStackPanel;
            this.layoutControlItem4.Location = new System.Drawing.Point(0, 440);
            this.layoutControlItem4.Name = "layoutControlItem4";
            this.layoutControlItem4.Size = new System.Drawing.Size(712, 310);
            this.layoutControlItem4.TextVisible = false;
            // 
            // lblSpace2
            // 
            this.lblSpace2.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblSpace2.Appearance.Options.UseFont = true;
            this.lblSpace2.Location = new System.Drawing.Point(170, 4);
            this.lblSpace2.Name = "lblSpace2";
            this.lblSpace2.Size = new System.Drawing.Size(3, 13);
            this.lblSpace2.TabIndex = 24;
            this.lblSpace2.Text = " ";
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 32);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(251, 10);
            // 
            // FormExternalData
            // 
            this.AllowFormGlass = DevExpress.Utils.DefaultBoolean.True;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoScroll = true;
            this.ClientSize = new System.Drawing.Size(712, 782);
            this.Controls.Add(this.layoutControl3);
            this.Controls.Add(this.ribbon);
            this.IconOptions.Image = global::uBuyFirst.Properties.Resources.uGrad;
            this.Name = "FormExternalData";
            this.Ribbon = this.ribbon;
            this.Text = "External Data";
            this.Load += new System.EventHandler(this.ExternalDataForm_Load);
            this.Resize += new System.EventHandler(this.FormExternalData_Resize);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl3)).EndInit();
            this.layoutControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.connectionTypeStackPanel)).EndInit();
            this.connectionTypeStackPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.connectionTypeHorStackPanel)).EndInit();
            this.connectionTypeHorStackPanel.ResumeLayout(false);
            this.connectionTypeHorStackPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkExternalDataEnabled.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioExternalDataGroups.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pythonInstallStackPanel)).EndInit();
            this.pythonInstallStackPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.stepsAndStartStackPanel)).EndInit();
            this.stepsAndStartStackPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.scriptStepsTabControl1)).EndInit();
            this.scriptStepsTabControl1.ResumeLayout(false);
            this.scriptStepsTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.csvAllstackPanel)).EndInit();
            this.csvAllstackPanel.ResumeLayout(false);
            this.csvAllstackPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.browseCsvStackPanel)).EndInit();
            this.browseCsvStackPanel.ResumeLayout(false);
            this.browseCsvStackPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCsvFileName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.csvstackPanelMatchingScript)).EndInit();
            this.csvstackPanelMatchingScript.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.stackPanel2)).EndInit();
            this.stackPanel2.ResumeLayout(false);
            this.stackPanel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.csvFieldsToUseForMatchListBoxControl)).EndInit();
            this.scriptStepsTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.step2StackPanel)).EndInit();
            this.step2StackPanel.ResumeLayout(false);
            this.step2StackPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.sentItemsStackPanel)).EndInit();
            this.sentItemsStackPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.stacklistFields)).EndInit();
            this.stacklistFields.ResumeLayout(false);
            this.stacklistFields.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkListUrlParams)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.stackCategoryFields)).EndInit();
            this.stackCategoryFields.ResumeLayout(false);
            this.stackCategoryFields.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkListUrlParamsCategories)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.sendFieldsDescSaveContStackPanel)).EndInit();
            this.sendFieldsDescSaveContStackPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkSendDescriptionAndPictures.Properties)).EndInit();
            this.scriptStepsTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.step3stackPanel)).EndInit();
            this.step3stackPanel.ResumeLayout(false);
            this.step3stackPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.matchingScriptEdit.Properties)).EndInit();
            this.scriptStepsTabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.step4stackPanel)).EndInit();
            this.step4stackPanel.ResumeLayout(false);
            this.step4stackPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.matchingTemplateEdit.Properties)).EndInit();
            this.scriptAITabPage.ResumeLayout(false);
            this.scriptAITabPage.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtApiKey.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.aiPromptEdit.Properties)).EndInit();
            this.scriptAIDisplayPage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.memoEditAIDisplayTemplate.Properties)).EndInit();
            this.tabAiColumns.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlAiColumns)).EndInit();
            this.layoutControlAiColumns.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.memoEditAiColumns.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.saveAndApplyStackPanel)).EndInit();
            this.saveAndApplyStackPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.previewStackPanel)).EndInit();
            this.previewStackPanel.ResumeLayout(false);
            this.previewStackPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtPreviewFinalUrl.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTemplateUrl.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.previewBrowserTitleStackPanel)).EndInit();
            this.previewBrowserTitleStackPanel.ResumeLayout(false);
            this.previewBrowserTitleStackPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelCefBrowser)).EndInit();
            this.panelCefBrowser.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.progressBarControlDownloadRequiredFiles.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PreviewControlGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.behaviorManager1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private DevExpress.XtraEditors.SimpleButton btnPreview;
        private DevExpress.XtraEditors.SimpleButton installPythonLinkButton;
        private DevExpress.XtraEditors.SimpleButton installPythonLibsButton;
        private DevExpress.XtraEditors.CheckedListBoxControl chkListUrlParams;
        private DevExpress.XtraEditors.LabelControl lblUrl;
        private DevExpress.XtraEditors.LabelControl lblPreviewResponse;
        private DevExpress.XtraEditors.MemoEdit txtTemplateUrl;
        private DevExpress.XtraEditors.MemoEdit txtPreviewFinalUrl;
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbon;
        private DevExpress.XtraEditors.PanelControl panelCefBrowser;
        private DevExpress.XtraEditors.ProgressBarControl progressBarControlDownloadRequiredFiles;
        private DevExpress.XtraEditors.SimpleButton btnDownloadRequiredFiles;
        private DevExpress.XtraEditors.SimpleButton btnManageChromeProfile;
        private DevExpress.XtraEditors.CheckEdit chkSendDescriptionAndPictures;
        private DevExpress.XtraEditors.CheckEdit chkExternalDataEnabled;
        private DevExpress.XtraEditors.RadioGroup radioExternalDataGroups;
        private DevExpress.XtraEditors.LabelControl lblConnectionType;
        private DevExpress.XtraEditors.LabelControl lblSpace;
        private DevExpress.XtraEditors.LabelControl lblSpace2;
        private DevExpress.XtraEditors.LabelControl lblStep1Local;
        private DevExpress.XtraEditors.TextEdit txtCsvFileName;
        private DevExpress.XtraEditors.LabelControl lblCsvFile;
        private DevExpress.XtraEditors.SimpleButton browseButtonCsv;
        private DevExpress.XtraEditors.CheckedListBoxControl csvFieldsToUseForMatchListBoxControl;
        private DevExpress.XtraEditors.LabelControl lblCsvFieldsToMatch;
        private DevExpress.XtraEditors.LabelControl lblStep2Local;
        private DevExpress.XtraEditors.CheckedListBoxControl chkListUrlParamsCategories;
        private DevExpress.XtraEditors.LabelControl lblStdListFields;
        private DevExpress.XtraEditors.LabelControl lblCategoryItemSpecList;
        private DevExpress.XtraEditors.SimpleButton SaveAndContCsvButton;
        private DevExpress.XtraEditors.SimpleButton SaveAndContFieldSenderButton;
        private DevExpress.XtraEditors.MemoEdit matchingScriptEdit;
        private DevExpress.XtraEditors.MemoEdit matchingTemplateEdit;
        private DevExpress.XtraEditors.LabelControl lblMatchingScript;
        private DevExpress.XtraEditors.LabelControl lblTemplateScript;
        private DevExpress.XtraEditors.SimpleButton SaveAndContMatchScriptButton;
        private DevExpress.XtraEditors.SimpleButton SaveAndContTemplateButton;
        private DevExpress.Utils.Behaviors.BehaviorManager behaviorManager1;
        private DevExpress.XtraEditors.SimpleButton newScriptButton;
        private DevExpress.XtraEditors.SimpleButton applyScriptButton;
        private DevExpress.XtraEditors.SimpleButton startScriptButton;
        private DevExpress.Utils.Layout.StackPanel browseCsvStackPanel;
        private DevExpress.Utils.Layout.StackPanel sendFieldsDescSaveContStackPanel;
        private DevExpress.Utils.Layout.StackPanel connectionTypeStackPanel;
        private DevExpress.Utils.Layout.StackPanel connectionTypeHorStackPanel;
        private DevExpress.Utils.Layout.StackPanel pythonInstallStackPanel;
        private DevExpress.Utils.Layout.StackPanel csvstackPanelMatchingScript;
        private DevExpress.Utils.Layout.StackPanel sentItemsStackPanel;
        private DevExpress.Utils.Layout.StackPanel stacklistFields;
        private DevExpress.Utils.Layout.StackPanel stackCategoryFields;
        private DevExpress.Utils.Layout.StackPanel stackPanel2;
        private DevExpress.Utils.Layout.StackPanel csvAllstackPanel;
        private DevExpress.Utils.Layout.StackPanel step2StackPanel;
        private DevExpress.Utils.Layout.StackPanel step3stackPanel;
        private DevExpress.Utils.Layout.StackPanel step4stackPanel;
        private DevExpress.Utils.Layout.StackPanel stepsAndStartStackPanel;
        private DevExpress.XtraTab.XtraTabControl scriptStepsTabControl1;
        private DevExpress.XtraTab.XtraTabPage scriptStepsTabPage1;
        private DevExpress.XtraTab.XtraTabPage scriptStepsTabPage2;
        private DevExpress.XtraTab.XtraTabPage scriptStepsTabPage3;
        private DevExpress.XtraTab.XtraTabPage scriptStepsTabPage4;
        private DevExpress.Utils.Layout.StackPanel previewStackPanel;
        private DevExpress.Utils.Layout.StackPanel previewBrowserTitleStackPanel;
        private DevExpress.XtraLayout.LayoutControlGroup PreviewControlGroup;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem4;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;
        private DevExpress.XtraLayout.LayoutControl layoutControl3;
        private DevExpress.Utils.Layout.StackPanel saveAndApplyStackPanel;
        private DevExpress.XtraTab.XtraTabPage scriptAITabPage;
        private LabelControl lblModel;
        private TextEdit txtApiKey;
        private MemoEdit aiPromptEdit;
        private LabelControl lblAiPrompt;
        private LabelControl lblApiKey;
        private LabelControl lblAIProviderDropdown;
        private System.Windows.Forms.ComboBox modelComboBox;
        private System.Windows.Forms.ComboBox aiProviderComboBox;
        private SimpleButton saveAIConfigurationButton;
        private DevExpress.XtraTab.XtraTabPage scriptAIDisplayPage;
        private MemoEdit memoEditAIDisplayTemplate;
        private SimpleButton saveAIConfigurationDisplayButton;
        private SimpleButton stopScriptButton;
        private DevExpress.XtraTab.XtraTabPage tabAiColumns;
        private LabelControl lblAiColumnsDescription;
        private SimpleButton btnSaveAiColumns;
        private MemoEdit memoEditAiColumns;
        private LayoutControl layoutControlAiColumns;
        private LayoutControlGroup Root;
        private LayoutControlItem layoutControlItem3;
        private LayoutControlItem layoutControlItem5;
        private LayoutControlItem layoutControlItem6;
        private EmptySpaceItem emptySpaceItem2;
        private EmptySpaceItem emptySpaceItem3;
        private LabelControl lblServerStatusControl;
        private LabelControl lblServerStatusControlStatus;

    }
}
