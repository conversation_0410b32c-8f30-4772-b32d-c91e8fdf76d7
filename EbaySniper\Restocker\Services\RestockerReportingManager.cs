﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// High-level manager for Restocker reporting functionality
    /// Provides simplified access to reporting features
    /// </summary>
    public class RestockerReportingManager
    {
        private readonly IReportService _reportService;
        private readonly IReportExportService _exportService;
        private readonly IPurchaseTrackerRepository _repository;

        public RestockerReportingManager(IPurchaseTrackerRepository repository)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _reportService = new ReportService(_repository);
            _exportService = new ReportExportService();
        }

        public RestockerReportingManager(IReportService reportService, IReportExportService exportService)
        {
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _exportService = exportService ?? throw new ArgumentNullException(nameof(exportService));
        }

        /// <summary>
        /// Generates and exports a user report for the last 30 days
        /// </summary>
        /// <param name="outputDirectory">Directory to save the report</param>
        /// <returns>Path to the generated report file</returns>
        public async Task<string> GenerateMonthlyUserReportAsync(string outputDirectory = null)
        {
            var filter = new ReportFilter
            {
                StartDate = DateTime.Now.AddDays(-30),
                EndDate = DateTime.Now,
                IncludeCompleted = true,
                IncludeFailed = true,
                IncludePending = true
            };

            var reports = await _reportService.GenerateUserReportAsync(filter);

            var fileName = $"restocker_user_report_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var filePath = outputDirectory != null
                ? Path.Combine(outputDirectory, fileName)
                : Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

            await _exportService.ExportUserReportToCsvAsync(reports, filePath);
            return filePath;
        }

        /// <summary>
        /// Generates a detailed transaction report for a specific job
        /// </summary>
        /// <param name="keywordId">The keyword ID</param>
        /// <param name="jobId">The job ID</param>
        /// <param name="outputDirectory">Directory to save the report</param>
        /// <returns>Path to the generated report file</returns>
        public async Task<string> GenerateJobDetailReportAsync(string keywordId, string jobId, string outputDirectory = null)
        {
            if (string.IsNullOrEmpty(keywordId))
                throw new ArgumentException("Keyword ID cannot be empty", nameof(keywordId));

            if (string.IsNullOrEmpty(jobId))
                throw new ArgumentException("Job ID cannot be empty", nameof(jobId));

            var report = await _reportService.GenerateTransactionDetailReportAsync(keywordId, jobId);

            var fileName = $"restocker_job_detail_{keywordId}_{jobId}_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var filePath = outputDirectory != null
                ? Path.Combine(outputDirectory, fileName)
                : Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

            await _exportService.ExportTransactionDetailReportToCsvAsync(report, filePath);
            return filePath;
        }

        /// <summary>
        /// Generates a custom user report based on filter criteria
        /// </summary>
        /// <param name="filter">Filter criteria for the report</param>
        /// <param name="outputDirectory">Directory to save the report</param>
        /// <returns>Path to the generated report file</returns>
        public async Task<string> GenerateCustomUserReportAsync(ReportFilter filter, string outputDirectory = null)
        {
            if (filter == null)
                throw new ArgumentNullException(nameof(filter));

            var reports = await _reportService.GenerateUserReportAsync(filter);

            var fileName = $"restocker_custom_report_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var filePath = outputDirectory != null
                ? Path.Combine(outputDirectory, fileName)
                : Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

            await _exportService.ExportUserReportToCsvAsync(reports, filePath);
            return filePath;
        }

        /// <summary>
        /// Saves HTML content from a purchase step and returns the file path
        /// </summary>
        /// <param name="htmlContent">The HTML content to save</param>
        /// <param name="keywordId">The keyword ID</param>
        /// <param name="jobId">The job ID</param>
        /// <returns>Path to the saved HTML file</returns>
        public async Task<string> SavePurchaseStepHtmlAsync(string htmlContent, string keywordId, string jobId)
        {
            return await _exportService.SaveHtmlContentAsync(htmlContent, keywordId, jobId, DateTime.Now);
        }

        /// <summary>
        /// Gets summary statistics for all purchase activities
        /// </summary>
        /// <param name="filter">Optional filter criteria</param>
        /// <returns>Summary statistics</returns>
        public async Task<ReportSummary> GetPurchaseSummaryAsync(ReportFilter filter = null)
        {
            return await _reportService.GetReportSummaryAsync(filter);
        }

        /// <summary>
        /// Cleans up old HTML files based on retention policy
        /// </summary>
        /// <param name="retentionDays">Number of days to retain files (default: 30)</param>
        /// <returns>Number of files deleted</returns>
        public async Task<int> CleanupOldHtmlFilesAsync(int retentionDays = 30)
        {
            return await _exportService.CleanupOldHtmlFilesAsync(retentionDays);
        }

        /// <summary>
        /// Gets the directory where HTML files are stored
        /// </summary>
        /// <returns>HTML storage directory path</returns>
        public string GetHtmlStorageDirectory()
        {
            return _exportService.GetHtmlStorageDirectory();
        }

        /// <summary>
        /// Generates a comprehensive report package including both user and detail reports
        /// </summary>
        /// <param name="outputDirectory">Directory to save the reports</param>
        /// <returns>List of generated report file paths</returns>
        public async Task<List<string>> GenerateComprehensiveReportPackageAsync(string outputDirectory = null)
        {
            var reportPaths = new List<string>();

            // Generate monthly user report
            var userReportPath = await GenerateMonthlyUserReportAsync(outputDirectory);
            reportPaths.Add(userReportPath);

            // Get summary statistics
            var summary = await GetPurchaseSummaryAsync();

            // Generate summary report file
            var summaryFileName = $"restocker_summary_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
            var summaryFilePath = outputDirectory != null
                ? Path.Combine(outputDirectory, summaryFileName)
                : Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), summaryFileName);

            File.WriteAllText(summaryFilePath, FormatSummaryReport(summary));
            reportPaths.Add(summaryFilePath);

            return reportPaths;
        }

        private string FormatSummaryReport(ReportSummary summary)
        {
            return $@"Restocker Purchase Summary Report
Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}

TRANSACTION SUMMARY
==================
Total Transactions: {summary.TotalTransactions}
Completed: {summary.CompletedTransactions}
Failed: {summary.FailedTransactions}
Pending: {summary.PendingTransactions}

FINANCIAL SUMMARY
================
Total Amount Spent: {summary.TotalAmountSpent:C}
Total Quantity Purchased: {summary.TotalQuantityPurchased}

ACTIVITY SUMMARY
===============
Unique Job IDs: {summary.UniqueJobIds}
Unique Keyword IDs: {summary.UniqueKeywordIds}

SUCCESS RATE
===========
Success Rate: {(summary.TotalTransactions > 0 ? (double)summary.CompletedTransactions / summary.TotalTransactions * 100 : 0):F2}%
Failure Rate: {(summary.TotalTransactions > 0 ? (double)summary.FailedTransactions / summary.TotalTransactions * 100 : 0):F2}%
";
        }
    }
}
